"""
POI Types and Attributes Definitions for TownVerse Atlas
"""

# Comprehensive list of POI types organized by categories
POI_CATEGORIES = {
    "DINING_NIGHTLIFE": [
        "restaurant",
        "cafe",
        "bar_pub",
        "nightclub",
        "bakery",
        "brewery_winery"
    ],
    "ACCOMMODATION": [
        "hotel",
        "motel",
        "bed_and_breakfast",
        "hostel",
        "vacation_rental",
        "campground"
    ],
    "CULTURAL_EDUCATIONAL": [
        "museum",
        "library",
        "art_gallery",
        "theater_cinema",
        "historical_site",
        "monument",
        "university",
        "school",
        "cultural_center"
    ],
    "RECREATION_ENTERTAINMENT": [
        "park",
        "playground",
        "sports_facility",
        "gym_fitness_center",
        "swimming_pool",
        "bowling_alley",
        "arcade",
        "zoo",
        "amusement_park"
    ],
    "SHOPPING_SERVICES": [
        "shopping_center",
        "department_store",
        "boutique",
        "market",
        "bookstore",
        "electronics_store",
        "bank",
        "post_office",
        "salon_spa",
        "dry_cleaner",
        "car_service"
    ],
    "RELIGIOUS": [
        "church",
        "mosque",
        "temple",
        "synagogue",
        "shrine"
    ],
    "TRANSPORTATION": [
        "airport",
        "train_station",
        "bus_stop",
        "metro_station",
        "taxi_stand",
        "car_rental",
        "ev_charging_station",
        "parking_lot"
    ],
    "HEALTHCARE": [
        "hospital",
        "clinic",
        "pharmacy",
        "dentist",
        "veterinarian"
    ],
    "GOVERNMENT_CIVIC": [
        "city_hall",
        "courthouse",
        "police_station",
        "fire_station",
        "public_service"
    ],
    "BUSINESS_PROFESSIONAL": [
        "office_building",
        "coworking_space",
        "convention_center",
        "business_center"
    ],
    "OUTDOOR_NATURE": [
        "hiking_trail",
        "beach",
        "lake_river",
        "forest",
        "scenic_viewpoint",
        "botanical_garden"
    ],
    "EVENTS_ACTIVITIES": [
        "event_venue",
        "conference_center",
        "festival_fair",
        "concert_hall",
        "sports_arena"
    ]
}

# Comprehensive list of all POI types
ALL_POI_TYPES = [
    "restaurant",
    "cafe",
    "bar_pub",
    "nightclub",
    "bakery",
    "brewery_winery",
    "hotel",
    "motel",
    "bed_and_breakfast",
    "hostel",
    "vacation_rental",
    "campground",
    "museum",
    "library",
    "art_gallery",
    "theater_cinema",
    "historical_site",
    "monument",
    "university",
    "school",
    "cultural_center",
    "park",
    "playground",
    "sports_facility",
    "gym_fitness_center",
    "swimming_pool",
    "bowling_alley",
    "arcade",
    "zoo",
    "amusement_park",
    "shopping_center",
    "department_store",
    "boutique",
    "market",
    "bookstore",
    "electronics_store",
    "bank",
    "post_office",
    "salon_spa",
    "dry_cleaner",
    "car_service",
    "church",
    "mosque",
    "temple",
    "synagogue",
    "shrine",
    "airport",
    "train_station",
    "bus_stop",
    "metro_station",
    "taxi_stand",
    "car_rental",
    "ev_charging_station",
    "parking_lot",
    "hospital",
    "clinic",
    "pharmacy",
    "dentist",
    "veterinarian",
    "city_hall",
    "courthouse",
    "police_station",
    "fire_station",
    "public_service",
    "office_building",
    "coworking_space",
    "convention_center",
    "business_center",
    "hiking_trail",
    "beach",
    "lake_river",
    "forest",
    "scenic_viewpoint",
    "botanical_garden",
    "event_venue",
    "conference_center",
    "festival_fair",
    "concert_hall",
    "sports_arena"
]

# Common attributes for all POIs
COMMON_POI_ATTRIBUTES = [
    "name",
    "type",
    "townId",
    "latitude",
    "longitude",
    "shortDescription",
    "fullDescription",
    "imagesIds",
    "featuredImageId",
    "rating",
    "numberOfReviews",
    "openingHours",
    "website",
    "phone",
    "address",
    "isActive",
    "lastUpdated",
    "source",
    "tags"  # To store original API type values
]

# Detailed attributes for each POI type group
POI_TYPE_ATTRIBUTES = {
    # Dining & Nightlife
    "restaurant": [
        "cuisineType",
        "priceRange",
        "menuHighlights",
        "allowsBooking",
        "hasMenu",
        "seatingCapacity",
        "dietaryOptions",
        "reservationsRequired"
    ],
    "cafe": [
        "cuisineType",
        "priceRange",
        "wifiAvailable",
        "seatingOptions",
        "coffeeSpecialties"
    ],
    "bar_pub": [
        "barType",
        "happyHourInfo",
        "drinkSpecialties",
        "ageRestriction",
        "liveMusic"
    ],
    "nightclub": [
        "musicGenre",
        "dressCode",
        "ageRestriction",
        "entryFee",
        "vipServices"
    ],
    "bakery": [
        "specialties",
        "openingHours",
        "deliveryAvailable"
    ],
    "brewery_winery": [
        "tourAvailability",
        "tastingOptions",
        "productionInfo"
    ],
    
    # Accommodation
    "hotel": [
        "hotelClass",
        "priceRange",
        "amenities",
        "allowsBooking",
        "roomTypes",
        "checkInTime",
        "checkOutTime",
        "petPolicy"
    ],
    "bed_and_breakfast": [
        "roomCount",
        "breakfastIncluded",
        "homeFeatures"
    ],
    "hostel": [
        "dormitoryTypes",
        "privateRooms",
        "curfewTime",
        "kitchenAccess"
    ],
    
    # Cultural & Educational
    "museum": [
        "admissionFee",
        "exhibitions",
        "collectionTypes",
        "guidedTours",
        "educationalPrograms"
    ],
    "library": [
        "services",
        "collections",
        "studySpaces",
        "digitalResources"
    ],
    "art_gallery": [
        "currentExhibition",
        "artistRepresented",
        "galleryType"
    ],
    "theater_cinema": [
        "performances",
        "seatingCapacity",
        "ticketingInfo"
    ],
    "historical_site": [
        "historicalPeriod",
        "significance",
        "guidedTourAvailability"
    ],
    
    # Recreation & Entertainment
    "park": [
        "facilities",
        "size",
        "walkingTrails",
        "playgrounds",
        "picnicAreas"
    ],
    "sports_facility": [
        "sportsTypes",
        "equipmentRental",
        "leagues",
        "trainingPrograms"
    ],
    "gym_fitness_center": [
        "equipmentTypes",
        "classSchedule",
        "personalTraining",
        "membershipOptions"
    ],
    "swimming_pool": [
        "poolTypes",
        "waterTemperature",
        "lifeguardService",
        "swimmingLessons"
    ],
    
    # Shopping & Services
    "shopping_center": [
        "storeCount",
        "anchorStores",
        "parkingInfo",
        "accessibilityFeatures"
    ],
    "boutique": [
        "productTypes",
        "brandCarried",
        "customServices"
    ],
    "market": [
        "marketType",
        "vendorCount",
        "operatingSchedule",
        "specialties"
    ],
    
    # Transportation
    "ev_charging_station": [
        "powerKW",
        "numberOfChargers",
        "plugTypes",
        "pricePerKWh",
        "open24h"
    ],
    "parking_lot": [
        "capacity",
        "hourlyRate",
        "securityFeatures",
        "accessibility"
    ],
    
    # Healthcare
    "hospital": [
        "emergencyServices",
        "specialties",
        "bedCount",
        "insuranceAccepted"
    ],
    "clinic": [
        "servicesOffered",
        "appointmentBooking",
        "insuranceAccepted"
    ],
    "pharmacy": [
        "services",
        "prescriptionFilling",
        "deliveryOptions"
    ],
    
    # Outdoor & Nature
    "hiking_trail": [
        "difficulty",
        "length",
        "estimatedTime",
        "trailSurface",
        "accessibility",
        "pointsOfInterest"
    ],
    "beach": [
        "beachType",
        "facilities",
        "waterQuality",
        "lifeguardService"
    ]
}

# Regrouping by common attributes
POI_GROUPS = {
    "booking_venues": [
        "restaurant",
        "hotel",
        "bar_pub",
        "event_venue",
        "conference_center"
    ],
    "cultural_educational": [
        "museum",
        "library",
        "art_gallery",
        "theater_cinema",
        "historical_site",
        "monument",
        "university",
        "school",
        "cultural_center"
    ],
    "recreation_facilities": [
        "park",
        "sports_facility",
        "gym_fitness_center",
        "swimming_pool",
        "playground",
        "bowling_alley",
        "arcade",
        "zoo",
        "amusement_park"
    ],
    "commercial_establishments": [
        "shopping_center",
        "department_store",
        "boutique",
        "market",
        "bookstore",
        "electronics_store",
        "bank",
        "post_office",
        "salon_spa",
        "dry_cleaner",
        "car_service"
    ],
    "transportation_infrastructure": [
        "ev_charging_station",
        "parking_lot",
        "airport",
        "train_station",
        "bus_stop",
        "metro_station"
    ],
    "healthcare_facilities": [
        "hospital",
        "clinic",
        "pharmacy",
        "dentist",
        "veterinarian"
    ],
    "outdoor_nature": [
        "hiking_trail",
        "beach",
        "lake_river",
        "forest",
        "scenic_viewpoint",
        "botanical_garden"
    ]
}