from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import datetime

class RawSourceDocument(BaseModel):
    """A base model for a raw document stored from an external source."""
    source_id: str = Field(..., description="The unique ID of the record from the source API.")
    fetched_at: datetime = Field(default_factory=datetime.utcnow)
    data_hash: str = Field(..., description="A hash of the raw data to detect changes.")
    data: Dict[str, Any] = Field(..., description="The raw, unaltered JSON data from the source.")
    model_config = {"extra": "allow"} # Allow extra fields

class MergedPOIDocument(BaseModel):
    """
    A model for the canonical, merged POI record in the data pool.
    This is the "golden record" before it is loaded into Appwrite.
    """
    id: Optional[str] = Field(alias="_id", default=None)
    townId: str
    name: str
    type: str
    latitude: float
    longitude: float
    
    # IDs from various sources
    osmId: Optional[str] = None
    googlePlaceId: Optional[str] = None
    foursquareFsqId: Optional[str] = None
    
    # Enriched content (JSON strings)
    shortDescription: Optional[str] = None
    fullDescription: Optional[str] = None
    
    # Media
    imagesIds: List[str] = []
    featuredImageId: Optional[str] = None
    
    # Details
    rating: float = 0.0
    numberOfReviews: int = 0
    priceRange: Optional[str] = None
    
    # Metadata
    hasARExperience: bool = False
    openingHours: Optional[str] = None
    
    # Processing state
    last_merged_at: datetime = Field(default_factory=datetime.utcnow)
    needs_enrichment: bool = True
    needs_appwrite_load: bool = True

    model_config = {"extra": "allow"}
