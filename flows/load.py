import json
from prefect import flow, task, get_run_logger
from appwrite.id import ID
from appwrite.client import AppwriteException
from appwrite.query import Query
from rapidfuzz import fuzz

from atlas.clients.appwrite import get_appwrite_client
from atlas.clients.mongo import get_mongo_db
from atlas.processing.comparator import compare_poi_data

# --- Configuration ---
DATABASE_ID = "town_verse_atlas_db_dev"
POIS_COLLECTION_ID = "POIs"
RESTAURANT_DETAILS_COLLECTION_ID = "RestaurantDetails"

@task
def upsert_poi_to_appwrite(poi_data: dict, restaurant_details_data: dict = None):
    """
    Task to perform the relational upsert for a single POI into Appwrite.
    This logic is migrated from the original ingest flow.
    """
    logger = get_run_logger()
    _, databases, _ = get_appwrite_client()

    try:
        logger.info(f"Upserting POI to Appwrite: {poi_data['name']}")

        # Step 1: Check if POI already exists using any available external ID
        queries = []
        if poi_data.get("osmId"):
            queries.append(Query.equal("osmId", poi_data["osmId"]))
        if poi_data.get("googlePlaceId"):
            queries.append(Query.equal("googlePlaceId", poi_data["googlePlaceId"]))
        if poi_data.get("foursquareFsqId"):
            queries.append(Query.equal("foursquareFsqId", poi_data["foursquareFsqId"]))

        existing_doc = None
        existing_doc_id = None
        if queries:
            try:
                existing_docs = databases.list_documents(
                    database_id=DATABASE_ID, collection_id=POIS_COLLECTION_ID, queries=queries,
                )
                if existing_docs['total'] > 0:
                    existing_doc = existing_docs['documents'][0]
                    existing_doc_id = existing_doc['$id']
            except AppwriteException as e:
                if e.code != 400: raise e
                logger.warning(f"Query failed, likely due to schema update pending.")

        if not existing_doc_id:
            lat_delta, lon_delta = 0.0015, 0.0015
            nearby_docs = databases.list_documents(
                database_id=DATABASE_ID, collection_id=POIS_COLLECTION_ID,
                queries=[
                    Query.equal("townId", poi_data["townId"]),
                    Query.greater_than_equal("latitude", poi_data["latitude"] - lat_delta),
                    Query.less_than_equal("latitude", poi_data["latitude"] + lat_delta),
                    Query.greater_than_equal("longitude", poi_data["longitude"] - lon_delta),
                    Query.less_than_equal("longitude", poi_data["longitude"] + lon_delta),
                ]
            )
            for doc in nearby_docs['documents']:
                if fuzz.token_set_ratio(poi_data['name'], doc['name']) > 90:
                    existing_doc = doc
                    existing_doc_id = doc['$id']
                    break

        # Clean data for Appwrite (remove Mongo-specific fields)
        clean_poi_data = poi_data.copy()
        clean_poi_data.pop('_id', None)
        clean_poi_data.pop('last_merged_at', None)
        clean_poi_data.pop('needs_appwrite_load', None)

        # If the POI exists, check if it has changed before updating
        if existing_doc_id and existing_doc:
            # Compare new data with existing data
            if compare_poi_data(clean_poi_data, existing_doc):
                logger.info(f"POI '{poi_data['name']}' is unchanged. Skipping update.")
                return existing_doc_id

        # Perform upsert
        if existing_doc_id:
            poi_doc = databases.update_document(
                database_id=DATABASE_ID, collection_id=POIS_COLLECTION_ID,
                document_id=existing_doc_id, data=clean_poi_data
            )
            logger.info(f"Updated existing POI document with ID: {poi_doc['$id']}")
        else:
            poi_doc = databases.create_document(
                database_id=DATABASE_ID, collection_id=POIS_COLLECTION_ID,
                document_id=ID.unique(), data=clean_poi_data
            )
            logger.info(f"Created new POI document with ID: {poi_doc['$id']}")

        if poi_data.get("type") == "Restaurant" and restaurant_details_data:
            details_data = restaurant_details_data.copy()
            details_data["poiId"] = poi_doc['$id']
            details_data.pop('_id', None)
            
            existing_details = databases.list_documents(
                database_id=DATABASE_ID, collection_id=RESTAURANT_DETAILS_COLLECTION_ID,
                queries=[Query.equal("poiId", poi_doc['$id'])]
            )
            if existing_details['total'] > 0:
                details_doc_id = existing_details['documents'][0]['$id']
                # For restaurant details, we'll always update for now
                databases.update_document(
                    database_id=DATABASE_ID, collection_id=RESTAURANT_DETAILS_COLLECTION_ID,
                    document_id=details_doc_id, data=details_data
                )
                logger.info(f"Updated RestaurantDetails for POI ID: {poi_doc['$id']}")
            else:
                databases.create_document(
                    database_id=DATABASE_ID, collection_id=RESTAURANT_DETAILS_COLLECTION_ID,
                    document_id=ID.unique(), data=details_data
                )
                logger.info(f"Created RestaurantDetails for POI ID: {poi_doc['$id']}")
        
        return poi_doc['$id']
    except AppwriteException as e:
        logger.error(f"An Appwrite error occurred during upsert: {e.message}")
        raise e

@flow(name="Load POIs to Appwrite")
def load_pois_to_appwrite(town_id: str = None):
    """
    Finds merged POIs that are ready to be loaded and upserts them into Appwrite.
    If town_id is provided, also handles soft deletes for POIs no longer in the source data.
    """
    logger = get_run_logger()
    db = get_mongo_db()
    _, databases, _ = get_appwrite_client()

    # Find merged POIs that need to be loaded to Appwrite
    query = {"needs_appwrite_load": True}
    if town_id:
        query["townId"] = town_id
        
    merged_pois_cursor = db.pois_merged.find(query)
    
    # Keep track of processed POI IDs for soft delete handling
    processed_poi_ids = set()

    for poi_doc in merged_pois_cursor:
        details_doc = None
        if poi_doc.get("type") == "Restaurant":
            details_doc = db.restaurant_details_merged.find_one({"_id": poi_doc["_id"]})
        
        try:
            appwrite_id = upsert_poi_to_appwrite(
                poi_data=poi_doc,
                restaurant_details_data=details_doc
            )
            # Track processed POI IDs
            processed_poi_ids.add(appwrite_id)
            
            # Unset the flag after successful load
            db.pois_merged.update_one(
                {"_id": poi_doc["_id"]},
                {"$set": {"needs_appwrite_load": False, "appwriteId": appwrite_id}}
            )
        except Exception as e:
            logger.error(f"Failed to load POI {poi_doc.get('name')} to Appwrite: {e}", exc_info=True)
    
    # Handle soft deletes if town_id is provided
    if town_id:
        logger.info(f"Handling soft deletes for town: {town_id}")
        # Get all POIs in Appwrite for this town
        try:
            all_town_pois = databases.list_documents(
                database_id=DATABASE_ID, 
                collection_id=POIS_COLLECTION_ID,
                queries=[Query.equal("townId", town_id)]
            )
            
            # Mark POIs that are no longer in the source data as inactive
            for poi in all_town_pois['documents']:
                if poi['$id'] not in processed_poi_ids and poi.get('isActive', True):
                    try:
                        databases.update_document(
                            database_id=DATABASE_ID, 
                            collection_id=POIS_COLLECTION_ID,
                            document_id=poi['$id'], 
                            data={"isActive": False}
                        )
                        logger.info(f"Marked POI {poi['name']} as inactive (soft delete)")
                    except Exception as e:
                        logger.error(f"Failed to soft delete POI {poi.get('name')}: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Failed to fetch existing POIs for soft delete handling: {e}", exc_info=True)

if __name__ == "__main__":
    # Example of how to run the full, decoupled pipeline
    # In production, these would be separate Prefect deployments with triggers.
    town = "vianden-test"
    bbox = "49.92,6.19,49.94,6.22"

    # 1. Extract raw data from all sources
    extract_pois_for_town(town_id=town, bbox=bbox)
    
    # 2. Transform the raw data into merged, enriched POIs
    transform_and_merge_pois(town_id=town)
    
    # 3. Load the final data into Appwrite (with soft delete handling)
    load_pois_to_appwrite(town_id=town)