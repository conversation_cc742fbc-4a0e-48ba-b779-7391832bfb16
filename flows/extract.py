import os
import json
import hashlib
from datetime import datetime
from prefect import flow, task, get_run_logger
from prefect.cache_policies import NO_CACHE
from pymongo.database import Database

from atlas.clients.mongo import get_mongo_db
from atlas.connectors.osm import fetch_pois_from_osm
from atlas.connectors.google import find_google_place_id, get_google_place_details
from atlas.connectors.foursquare import search_foursquare_places

# --- Tasks ---

@task(cache_policy=NO_CACHE)
def upsert_raw_data(db: Database, collection_name: str, source_id: str, data: dict) -> bool:
    """
    Upserts raw data into a specified MongoDB collection if it's new or has changed.
    Returns True if data was upserted, False otherwise.
    """
    logger = get_run_logger()
    collection = db[collection_name]
    
    # Use Pydantic or a stable JSON dump for consistent hashing
    data_str = json.dumps(data, sort_keys=True, default=str)
    data_hash = hashlib.md5(data_str.encode()).hexdigest()

    existing_doc = collection.find_one({"source_id": source_id})

    if existing_doc and existing_doc.get("data_hash") == data_hash:
        logger.debug(f"Data for {source_id} in {collection_name} is unchanged. Skipping.")
        return False

    logger.info(f"Upserting data for {source_id} into {collection_name}.")
    collection.update_one(
        {"source_id": source_id},
        {
            "$set": {
                "data": data,
                "data_hash": data_hash
            },
            "$setOnInsert": {
                "fetched_at": datetime.utcnow()
            }
        },
        upsert=True
    )
    return True

# --- Main Extraction Flow ---

@flow(name="Extract Town POIs")
def extract_pois_for_town(town_id: str, bbox: str):
    """
    Main extraction flow. Fetches data from OSM as the primary source,
    then finds corresponding POIs in Google and Foursquare, storing all
    raw responses in a MongoDB data pool.
    """
    logger = get_run_logger()
    logger.info(f"Starting raw data extraction for town: {town_id}")
    db = get_mongo_db()

    # 1. Fetch from OpenStreetMap
    osm_pois_raw = fetch_pois_from_osm(bbox)
    if not osm_pois_raw:
        logger.warning("No POIs found in OSM. Extraction flow is ending.")
        return

    logger.info(f"Found {len(osm_pois_raw)} potential POIs in OSM. Checking for updates...")
    for osm_poi in osm_pois_raw:
        osm_id = f"osm/{osm_poi['type']}/{osm_poi['id']}"
        upsert_raw_data(db, "source_osm_raw", osm_id, osm_poi)

        tags = osm_poi.get("tags", {})
        poi_name = tags.get("name")
        if not poi_name:
            continue

        if osm_poi["type"] == "node":
            lat, lon = osm_poi.get("lat"), osm_poi.get("lon")
        else:
            center = osm_poi.get("center", {})
            lat, lon = center.get("lat"), center.get("lon")

        if not lat or not lon:
            continue
        
        links = {"_id": osm_id}

        # 2. Find matches in Google Places
        try:
            google_place_id = find_google_place_id(f"{poi_name}, {town_id}", lat, lon)
            if google_place_id:
                links["googlePlaceId"] = google_place_id
                google_details_raw = get_google_place_details(google_place_id)
                if google_details_raw:
                    upsert_raw_data(db, "source_google_raw", google_details_raw['id'], google_details_raw)
        except Exception as e:
            logger.error(f"Failed Google extraction for '{poi_name}': {e}", exc_info=True)

        # 3. Find matches in Foursquare
        try:
            fs_place_raw = search_foursquare_places(poi_name, lat, lon)
            if fs_place_raw:
                # Check for fsq_id or fsq_place_id (Foursquare API uses different field names)
                fsq_id = fs_place_raw.get("fsq_id") or fs_place_raw.get("fsq_place_id")
                if fsq_id:
                    links["foursquareFsqId"] = fsq_id
                    upsert_raw_data(db, "source_foursquare_raw", fsq_id, fs_place_raw)
        except Exception as e:
            logger.error(f"Failed Foursquare extraction for '{poi_name}': {e}", exc_info=True)
            
        # 4. Store the discovered links
        if len(links) > 1:
            db.poi_links.update_one({"_id": osm_id}, {"$set": links}, upsert=True)
