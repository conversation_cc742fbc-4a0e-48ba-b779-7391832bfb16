import os
import json
from datetime import datetime
from prefect import flow, task, get_run_logger
from appwrite.id import ID

from atlas.clients.mongo import get_mongo_db
from atlas.processing.mapper import map_osm_to_canonical, map_google_to_canonical, map_foursquare_to_canonical
from atlas.processing.merger import merge_poi_data
from atlas.processing.detail_merger import create_detail_document
from atlas.processing.enricher import enrich_poi_with_llm
from atlas.processing.media import download_image, process_image_from_bytes, upload_image_to_appwrite

@flow(name="Transform and Merge POIs")
def transform_and_merge_pois(town_id: str):
    """
    Reads from raw data collections, merges POI data into a canonical model,
    enriches it with LLM-generated content, processes images, and stores
    the result in the `pois_merged` collection.
    """
    logger = get_run_logger()
    db = get_mongo_db()

    # In a production system, you would query for documents that were recently updated.
    # For this implementation, we process all available links.
    links_cursor = db.poi_links.find({})
    
    # Fetch town configuration for language settings
    try:
        config_doc = db.TownConfigurations.find_one({"_id": town_id})
        available_languages = config_doc.get("availableLanguages", ["en"]) if config_doc else ["en"]
    except Exception:
        available_languages = ["en"]
    logger.info(f"Will generate descriptions for languages: {available_languages}")

    for links in links_cursor:
        osm_id = links["_id"]
        google_id = links.get("googlePlaceId")
        fsq_id = links.get("foursquareFsqId")
        
        logger.info(f"Processing POI with OSM ID: {osm_id}")

        all_poi_data = []
        
        # 1. Map OSM data
        osm_raw = db.source_osm_raw.find_one({"source_id": osm_id})
        if not osm_raw:
            logger.warning(f"Skipping {osm_id}: Raw OSM data not found.")
            continue
        osm_canonical, osm_details = map_osm_to_canonical(osm_raw['data'], town_id)
        if osm_canonical: all_poi_data.append((osm_canonical, osm_details))

        # 2. Map Google data
        if google_id:
            google_raw = db.source_google_raw.find_one({"source_id": google_id})
            if google_raw:
                google_canonical, google_details = map_google_to_canonical(google_raw['data'], town_id, osm_id)
                if google_canonical: all_poi_data.append((google_canonical, google_details))
        
        # 3. Map Foursquare data
        if fsq_id:
            fsq_raw = db.source_foursquare_raw.find_one({"source_id": fsq_id})
            if fsq_raw:
                fs_canonical, fs_details = map_foursquare_to_canonical(fsq_raw['data'], town_id, osm_id)
                if fs_canonical: all_poi_data.append((fs_canonical, fs_details))

        if not all_poi_data: continue

        # 4. Merge data
        merged_poi, merged_details = merge_poi_data(all_poi_data)
        if not merged_poi: continue

        # 5. Enrich with LLM-generated descriptions
        try:
            merged_poi = enrich_poi_with_llm.with_options(task_run_name=f"Enrich '{merged_poi.get('name')}'")(merged_poi, available_languages)
        except Exception as e:
            logger.error(f"Failed LLM enrichment for '{merged_poi.get('name')}': {e}", exc_info=True)

        # 6. Process and upload images
        photo_refs = merged_poi.pop("_photo_references", [])
        if photo_refs:
            api_key = os.getenv("GOOGLE_PLACES_API_KEY")
            if api_key and photo_refs:
                photo_url = f"https://maps.googleapis.com/maps/api/place/photo?maxwidth=2000&photoreference={photo_refs[0]}&key={api_key}"
                image_bytes = download_image(photo_url)
                if image_bytes:
                    processed_images = process_image_from_bytes(image_bytes)
                    if processed_images:
                        image_id_map = {}
                        poi_file_id = merged_poi.get("osmId") or merged_poi.get("googlePlaceId") or ID.unique()
                        for size_name, img_bytes in processed_images.items():
                            filename = f"{poi_file_id}_{size_name}.webp"
                            file_id = upload_image_to_appwrite(img_bytes, filename)
                            if file_id: image_id_map[size_name] = file_id
                        
                        if image_id_map:
                            merged_poi['imagesIds'] = list(image_id_map.values())
                            merged_poi['featuredImageId'] = image_id_map.get('hero')

        # 7. Create detail document based on POI group
        detail_collection_name, detail_document = create_detail_document(merged_poi, merged_details)
        
        # 8. Upsert to pois_merged collection
        merged_poi["last_merged_at"] = datetime.utcnow()
        merged_poi["needs_appwrite_load"] = True # Flag for the load flow
        db.pois_merged.update_one({"_id": osm_id}, {"$set": merged_poi}, upsert=True)
        
        # 9. Upsert detail document to the appropriate detail collection
        if detail_collection_name and detail_document:
            # Create the detail collection name with "_merged" suffix
            detail_collection_name_merged = f"{detail_collection_name}_merged"
            detail_document["poiId"] = osm_id
            db[detail_collection_name_merged].update_one({"_id": osm_id}, {"$set": detail_document}, upsert=True)
        
        # 10. Handle legacy restaurant details (for backward compatibility)
        if merged_details and merged_poi.get("type", "").lower() == "restaurant":
             merged_details["poiId"] = osm_id
             db.restaurant_details_merged.update_one({"_id": osm_id}, {"$set": merged_details}, upsert=True)

        logger.info(f"Successfully transformed and merged POI: {merged_poi.get('name')}")
