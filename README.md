# **TownVerse Atlas**

**Keeps every town on the map.**

TownVerse Atlas is the central data ingestion and enrichment pipeline for the TownVerse platform. Its mission is to automatically populate TownVerse with comprehensive, accurate, and engaging data for Points of Interest (POIs) like restaurants, hotels, museums, and landmarks. It solves the challenge of manual data entry by programmatically fetching, normalizing, enriching, and storing POI data at scale.

## **Table of Contents**

* [Architecture Flow](#architecture-flow)  
* [Features](#features)  
* [Tech Stack](#tech-stack)  
* [Data Sources](#data-sources)  
* [Appwrite Integration](#appwrite-integration)  
* [Getting Started](#getting-started)  
  * [Prerequisites](#prerequisites)  
  * [Installation](#installation)  
  * [Running the Pipeline](#running-the-pipeline)  
* [Configuration](#configuration)  
* [Project Structure](#project-structure)  
* [Roadmap](#roadmap)  
* [Contributing](#contributing)

## **Architecture Flow**

The pipeline operates in a series of orchestrated stages, ensuring data is processed consistently and reliably.

1. **Trigger**: A task is initiated, either via a CRON schedule (e.g., nightly) or a manual API call, targeting a specific town.  
2. **Fetch**: Raw POI data is pulled from multiple public APIs and open data sources.  
3. **Normalize & Deduplicate**: Data from all sources is mapped to a canonical TownVerse schema. Records are fuzzy-matched against existing data in Appwrite to prevent duplicates.  
4. **Enrich**: An LLM (Gemini 2.5 Flash with GPT-4o as fallback) generates compelling, multilingual descriptions for each POI.  
5. **Media Pipeline**: Attached photos are downloaded, resized into multiple formats (thumbnail, medium, hero), converted to WebP, and uploaded to a dedicated Appwrite storage bucket.  
6. **Upsert**: The final, enriched POI data is inserted or updated (upserted) into the appropriate Appwrite database collections.  
7. **Orchestration**: The entire workflow is managed by Prefect, providing automatic retries, logging, and observability.

```mermaid
graph TD
    A[Trigger: Cron/API] --> B{Fetch};
    B --> C[Normalize & Dedup];
    C --> D[Enrich with LLM];
    D --> E[Process Images];
    E --> F{Upsert to Appwrite};
    F --> G[Done];
    F --> H[Soft Delete Missing];
    H --> G;

    subgraph Data Sources
        B1[OpenStreetMap]
        B2[Google Places]
        B3[Foursquare]
        B4[Local Open Data]
    end

    subgraph Appwrite Backend
        F1[DB: POIs]
        F2[DB: RestaurantDetails, etc.]
        F3[Storage: poi-images]
    end

    B1 & B2 & B3 & B4 --> C;
    F --> F1 & F2 & F3;

```


## **Features**

* **Multi-Source Aggregation**: Fetches data from OpenStreetMap, Google Places, Foursquare, and local data portals to create the most complete record.  
* **Intelligent Deduplication**: Uses fuzzy string matching (RapidFuzz) and geographic proximity to identify and merge duplicate POIs.  
* **AI-Powered Enrichment**: Leverages Gemini 2.5 Flash (with OpenAI GPT-4o as fallback) to generate high-quality, engaging descriptions in multiple languages.  
* **Robust Media Handling**: Automatically processes and optimizes images for web and mobile, handling various sizes and formats.  
* **Idempotent Upserts**: Safely runs multiple times without creating duplicate entries, updating records only when changes are detected.  
* **Scalable & Containerized**: Packaged in a Docker container for easy deployment on any cloud provider (Google Cloud Run, AWS Fargate) or a simple VM.  
* **Orchestrated & Observable**: Managed by Prefect for reliable execution, scheduling, retries, and monitoring.  
* **Incremental Updates**: Only updates records when data has changed and gracefully handles deleted POIs with soft deletes.

## **Tech Stack**

* **Core Language**: Python 3.12+  
* **Orchestration**: Prefect 2  
* **Containerization**: Docker  
* **Backend**: Appwrite (Database & Storage)  
* **Data Processing**: Pandas  
* **AI/ML**: Google Gemini API (Gemini 2.5 Flash with OpenAI GPT-4o as fallback)  
* **Image Processing**: pyvips  
* **APIs**: requests, appwrite-sdk

## **Data Sources**

| Source | Priority | Data Extracted |
| :---- | :---- | :---- |
| **Google Places API** | 1 | Rich categories, ratings, photos, price level, status |
| **Foursquare Places API** | 2 | Popularity, real-time flags, secondary photos |
| **Local Open Data Portals** | 3 | Official tourism POIs, heritage sites, event schedules |
| **OpenStreetMap (Overpass)** | 4 | Name, type, lat/lon, tags, opening hours, wiki links |

## **Appwrite Integration**

Atlas is tightly coupled with the TownVerse Appwrite project.

* **Database**: town\_verse\_atlas\_db  
* **Primary Collection**: POIs (Points Of Interest) serves as the master record for all locations.  
* **Detail Collections**: Type-specific data is stored in related collections like RestaurantDetails, HotelDetails, etc., linked by the master poiId.  
* **Storage Buckets**: Images are stored in dedicated buckets, primarily poi-images, but also restaurant-images and hotel-images where applicable.  
* **Authentication**: The service authenticates using a dedicated, scoped API key with databases.write and storage.write permissions.

## **Getting Started**

### **Prerequisites**

* Docker Desktop  
* Python 3.12+  
* An Appwrite project (Cloud or self-hosted)  
* API keys for:  
  * Appwrite  
  * Google Gemini (or OpenAI as fallback)  
  * Google Cloud (with Places API enabled)  
  * Foursquare

### **Installation**

1.  **Clone the repository:**
    ```bash
    git clone <your-repo-url>/townverse-atlas.git
    cd townverse-atlas
    ```

2.  **Configure environment variables:** Create a `.env` file in the project root by copying the example file:
    ```bash
    cp .env.example .env
    ```
    Now, fill in the `.env` file with your API keys and Appwrite project details. The default `MONGO_CONNECTION_STRING` is configured to work with the Docker Compose setup provided.
    ```env
    # .env
    # Appwrite
    APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
    APPWRITE_PROJECT_ID=YOUR_PROJECT_ID
    APPWRITE_API_KEY=YOUR_SECRET_API_KEY

    # MongoDB - for the local Docker Compose setup
    # The hostname 'mongo' matches the service name in docker-compose.yml
    MONGO_CONNECTION_STRING=*******************************/

    # AI Providers (Gemini is default, OpenAI as fallback)
    GEMINI_API_KEY=...
    # OPENAI_API_KEY=sk-...  # Optional fallback

    # Other APIs
    GOOGLE_PLACES_API_KEY=AIza...
    FOURSQUARE_API_KEY=...
    ```

### **Running the Pipeline**

This project uses Docker Compose to simplify the management of the application and its MongoDB dependency.

1.  **Start the services:** This command will build the `atlas` image if it doesn't exist, and start both the `atlas` and `mongo` containers. The `-d` flag runs them in the background.
    ```bash
    docker-compose up -d --build
    ```

2.  **Execute the ingestion flows:** You can run the flows by executing commands inside the running `atlas` container.
    ```bash
    # Example for Vianden, Luxembourg
    # 1. Extract raw data from sources into MongoDB
    docker-compose exec atlas python -c 'from flows.extract import extract_pois_for_town; extract_pois_for_town("vianden", "49.92,6.19,49.94,6.22")'

    # 2. Transform raw data into merged POIs
    docker-compose exec atlas python -c 'from flows.transform import transform_and_merge_pois; transform_and_merge_pois("vianden")'

    # 3. Load merged POIs into Appwrite
    docker-compose exec atlas python -c 'from flows.load import load_pois_to_appwrite; load_pois_to_appwrite("vianden")'
    ```

3.  **View logs:** To see the output from the pipeline, you can view the logs for the `atlas` service.
    ```bash
    docker-compose logs -f atlas
    ```

4.  **Stop the services:** When you are finished, you can stop the containers. The `-v` flag removes the named volume used by MongoDB, clearing its data. Omit this flag if you want to preserve the database between runs.
    ```bash
    docker-compose down -v
    ```

## **Configuration**

The pipeline is configured via environment variables, as defined in the .env file.

| Variable | Description |
| :---- | :---- |
| APPWRITE\_ENDPOINT | Your Appwrite instance's API endpoint. |
| APPWRITE\_PROJECT\_ID | The ID of your TownVerse Appwrite project. |
| APPWRITE\_API\_KEY | A server-side API key with read/write permissions. |
| GEMINI\_API\_KEY | Your API key for the Google Gemini API (default provider). |
| OPENAI\_API\_KEY | Your secret key for the OpenAI API (fallback provider). |
| AI\_PROVIDER | Preferred AI provider ("gemini" or "openai"). Defaults to "gemini". |
| GOOGLE\_PLACES\_API\_KEY | Your API key for the Google Places API. |
| FOURSQUARE\_API\_KEY | Your API key for the Foursquare Places API. |

## **Project Structure**

townverse-atlas/
├── docker-compose.yml      \# Docker Compose setup for local development
├── Dockerfile              \# Container definition  
├── README.md               \# This file  
├── requirements.txt        \# Python dependencies  
├── .env.example            \# Example environment file  
├── atlas/                  \# Core Python source package  
│   ├── \_\_init\_\_.py  
│   ├── clients/            \# Appwrite and MongoDB clients  
│   │   ├── appwrite.py     \# Appwrite SDK setup  
│   │   └── mongo.py        \# MongoDB connection  
│   ├── connectors/         \# Modules for fetching data  
│   │   ├── osm.py          \# OpenStreetMap connector  
│   │   ├── google.py       \# Google Places API connector  
│   │   ├── foursquare.py   \# Foursquare Places API connector  
│   │   └── pexels.py       \# Pexels image connector (future use)  
│   └── processing/         \# Modules for data processing  
│       ├── mapper.py       \# Data mapping to canonical model  
│       ├── merger.py       \# Data merging from multiple sources  
│       ├── enricher.py     \# LLM enrichment with descriptions  
│       └── media.py        \# Image processing and upload  
├── flows/                  \# Prefect flows that define the pipeline  
│   ├── extract.py          \# Extract raw data from sources  
│   ├── transform.py        \# Transform and merge data  
│   └── load.py             \# Load data into Appwrite  
├── staging/                \# Data models for staging  
│   ├── \_\_init\_\_.py  
│   └── models.py           \# Pydantic models for data validation  
└── utils/                  \# Utility scripts  
    ├── \_\_init\_\_.py  
    └── clear_poi_images_bucket.py  \# Utility to clear image bucket

## **Roadmap**

The development of TownVerse Atlas is planned in an incremental, sprint-based approach:

* **Sprint 1**: Docker skeleton & Appwrite write-test.  
* **Sprint 2**: OSM connector, deduplication logic, image upload.  
* **Sprint 3**: GPT-4o enrichment, multilingual support.  
* **Sprint 4**: Paid API connectors (Google & Foursquare) & data merging.  
* **Sprint 5**: Incremental update mode (diffs & soft deletes).  
* **Sprint 6+**: Admin web UI for human review and user-suggested edits.

## **Contributing**

Contributions are welcome\! Please open an issue to discuss a new feature or bug. Pull requests should be targeted at the develop branch.
