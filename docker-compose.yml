services:
  mongo:
    image: mongo:latest
    container_name: townverse-mongo
    environment:
      - MONGO_INITDB_ROOT_USERNAME=user
      - MONGO_INITDB_ROOT_PASSWORD=pass
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - townverse-net

  atlas:
    build: .
    container_name: townverse-atlas
    env_file: .env
    depends_on:
      - mongo
    networks:
      - townverse-net
    volumes:
      - .:/app

networks:
  townverse-net:
    driver: bridge

volumes:
  mongo-data:
    driver: local
