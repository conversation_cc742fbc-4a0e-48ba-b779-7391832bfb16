{"projectId": "68250369001559db37ff", "projectName": "townverse_dev", "databases": [{"$id": "town_verse_atlas_db_dev", "name": "TownVerseAtlasDBDev", "enabled": true}], "collections": [{"$id": "POIs", "databaseId": "town_verse_atlas_db_dev", "name": "Points Of Interest", "enabled": true, "documentSecurity": false, "$permissions": ["create(\"team:admins\")", "read(\"any\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "attributes": [{"key": "townId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "name", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "type", "type": "string", "status": "available", "required": true, "array": false, "size": 100}, {"key": "latitude", "type": "double", "status": "available", "required": true, "array": false}, {"key": "longitude", "type": "double", "status": "available", "required": true, "array": false}, {"key": "shortDescription", "type": "string", "status": "available", "required": true, "array": false, "size": 8000}, {"key": "fullDescription", "type": "string", "status": "available", "required": true, "array": false, "size": 150000}, {"key": "imagesIds", "type": "string", "status": "available", "required": false, "array": true, "size": 2048}, {"key": "rating", "type": "double", "status": "available", "required": false, "array": false, "default": 0}, {"key": "numberOfReviews", "type": "integer", "status": "available", "required": false, "array": false, "default": 0}, {"key": "openingHours", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "entryFee", "type": "string", "status": "available", "required": false, "array": false, "size": 2000}, {"key": "hasARExperience", "type": "boolean", "status": "available", "required": false, "array": false, "default": false}, {"key": "arExperienceId", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "osmId", "type": "string", "status": "available", "required": false, "array": false, "size": 32}, {"key": "featuredImageId", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "googlePlaceId", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "foursquareFsqId", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "source", "type": "string", "status": "available", "required": false, "array": false, "size": 255}], "indexes": [{"key": "idx_townId", "type": "key", "status": "available", "attributes": ["townId"], "orders": ["ASC"]}, {"key": "idx_townId_type", "type": "key", "status": "available", "attributes": ["townId", "type"], "orders": ["ASC", "ASC"]}, {"key": "idx_name", "type": "fulltext", "status": "available", "attributes": ["name"], "orders": ["ASC"]}, {"key": "idx_townId_geo", "type": "key", "status": "available", "attributes": ["townId", "latitude", "longitude"], "orders": ["ASC", "ASC", "ASC"]}, {"key": "idx_osmId", "type": "key", "status": "available", "attributes": ["osmId"], "orders": ["ASC"]}, {"key": "idx_googlePlaceId", "type": "key", "status": "available", "attributes": ["googlePlaceId"], "orders": ["ASC"]}, {"key": "idx_foursquareFsqId", "type": "key", "status": "available", "attributes": ["foursquareFsqId"], "orders": ["ASC"]}]}, {"$id": "RestaurantDetails", "databaseId": "town_verse_atlas_db_dev", "name": "Restaurant Details", "enabled": true, "documentSecurity": false, "$permissions": ["create(\"team:admins\")", "create(\"team:business_owners\")", "read(\"any\")", "update(\"team:admins\")", "update(\"team:business_owners\")", "delete(\"team:admins\")"], "attributes": [{"key": "poiId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "cuisineType", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "priceRange", "type": "string", "status": "available", "required": false, "array": false, "size": 10}, {"key": "allowsBooking", "type": "boolean", "status": "available", "required": true, "array": false}, {"key": "menuHighlightsJson", "type": "string", "status": "available", "required": false, "array": false, "size": 64000}, {"key": "ownerUserId", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "commercialPriority", "type": "integer", "status": "available", "required": false, "array": false}, {"key": "adoptionIndex", "type": "integer", "status": "available", "required": false, "array": false}, {"key": "hasMenu", "type": "boolean", "status": "available", "required": false, "array": false, "default": false}], "indexes": [{"key": "idx_poiId", "type": "key", "status": "available", "attributes": ["poiId"], "orders": ["ASC"]}, {"key": "idx_cuisineType", "type": "fulltext", "status": "available", "attributes": ["cuisineType"], "orders": ["ASC"]}, {"key": "idx_priority_adopt", "type": "key", "status": "available", "attributes": ["commercialPriority", "adoptionIndex"], "orders": ["ASC", "ASC"]}]}, {"$id": "RestaurantMenuItems", "databaseId": "town_verse_atlas_db_dev", "name": "Restaurant Menu Items", "enabled": true, "documentSecurity": false, "$permissions": ["create(\"team:admins\")", "create(\"team:business_owners\")", "read(\"any\")", "update(\"team:admins\")", "update(\"team:business_owners\")", "delete(\"team:admins\")"], "attributes": [{"key": "poiId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "sectionSlug", "type": "string", "status": "available", "required": true, "array": false, "size": 100}, {"key": "sectionTitle", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "itemName", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "ingredients", "type": "string", "status": "available", "required": false, "array": false, "size": 4000}, {"key": "allergensJson", "type": "string", "status": "available", "required": false, "array": true, "size": 255}, {"key": "price", "type": "double", "status": "available", "required": true, "array": false}, {"key": "position", "type": "integer", "status": "available", "required": false, "array": false}], "indexes": [{"key": "idx_poiId", "type": "key", "status": "available", "attributes": ["poiId"], "orders": ["ASC"]}, {"key": "idx_poiId_section", "type": "key", "status": "available", "attributes": ["poiId", "sectionSlug", "position"], "orders": ["ASC", "ASC", "ASC"]}]}, {"$id": "HotelDetails", "databaseId": "town_verse_atlas_db_dev", "name": "Hotel Details", "enabled": true, "documentSecurity": false, "$permissions": ["create(\"team:admins\")", "create(\"team:business_owners\")", "read(\"any\")", "update(\"team:admins\")", "update(\"team:business_owners\")", "delete(\"team:admins\")"], "attributes": [{"key": "poiId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "hotelClass", "type": "integer", "status": "available", "required": false, "array": false}, {"key": "priceRange", "type": "string", "status": "available", "required": false, "array": false, "size": 10}, {"key": "<PERSON><PERSON>son", "type": "string", "status": "available", "required": false, "array": false, "size": 64000}, {"key": "allowsBooking", "type": "boolean", "status": "available", "required": true, "array": false}, {"key": "ownerUserId", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "commercialPriority", "type": "integer", "status": "available", "required": false, "array": false}, {"key": "adoptionIndex", "type": "integer", "status": "available", "required": false, "array": false}], "indexes": [{"key": "idx_poiId", "type": "key", "status": "available", "attributes": ["poiId"], "orders": ["ASC"]}]}, {"$id": "EventDetails", "databaseId": "town_verse_atlas_db_dev", "name": "Event Details", "enabled": true, "documentSecurity": false, "$permissions": ["create(\"team:admins\")", "read(\"any\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "attributes": [{"key": "poiId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "eventDate", "type": "datetime", "status": "available", "required": true, "array": false}, {"key": "eventEndDate", "type": "datetime", "status": "available", "required": false, "array": false}, {"key": "venueName", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "ticketPrice", "type": "string", "status": "available", "required": false, "array": false, "size": 2000}, {"key": "organizerUserId", "type": "string", "status": "available", "required": false, "array": false, "size": 255}], "indexes": [{"key": "idx_poiId", "type": "key", "status": "available", "attributes": ["poiId"], "orders": ["ASC"]}, {"key": "idx_eventDate", "type": "key", "status": "available", "attributes": ["eventDate"], "orders": ["ASC"]}]}, {"$id": "TrailDetails", "databaseId": "town_verse_atlas_db_dev", "name": "Trail Details", "enabled": true, "documentSecurity": false, "$permissions": ["create(\"team:admins\")", "read(\"any\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "attributes": [{"key": "poiId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "difficulty", "type": "string", "status": "available", "required": false, "array": false, "size": 1000}, {"key": "length", "type": "double", "status": "available", "required": false, "array": false}, {"key": "estimatedTime", "type": "integer", "status": "available", "required": false, "array": false}, {"key": "routeGeoJson", "type": "string", "status": "available", "required": false, "array": false, "size": 10000}, {"key": "pointsOfInterestAlongTrail", "type": "string", "status": "available", "required": false, "array": true, "size": 255}], "indexes": [{"key": "idx_poiId", "type": "key", "status": "available", "attributes": ["poiId"], "orders": ["ASC"]}, {"key": "idx_difficulty", "type": "fulltext", "status": "available", "attributes": ["difficulty"], "orders": ["ASC"]}]}, {"$id": "EVChargingStationDetails", "databaseId": "town_verse_atlas_db_dev", "name": "EV Charging Station Details", "enabled": true, "documentSecurity": false, "$permissions": ["create(\"team:admins\")", "read(\"any\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "attributes": [{"key": "poiId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "powerKW", "type": "double", "status": "available", "required": false, "array": false}, {"key": "numberOfChargers", "type": "integer", "status": "available", "required": false, "array": false}, {"key": "plugTypesJson", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "pricePerKWh", "type": "string", "status": "available", "required": false, "array": false, "size": 50}, {"key": "open24h", "type": "boolean", "status": "available", "required": false, "array": false}], "indexes": [{"key": "idx_poiId", "type": "key", "status": "available", "attributes": ["poiId"], "orders": ["ASC"]}]}, {"$id": "PublicArtDetails", "databaseId": "town_verse_atlas_db_dev", "name": "Public Art Details", "enabled": true, "documentSecurity": false, "$permissions": ["create(\"team:admins\")", "read(\"any\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "attributes": [{"key": "poiId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "<PERSON><PERSON><PERSON>", "type": "string", "status": "available", "required": false, "array": false, "size": 4000}, {"key": "yearInstalled", "type": "integer", "status": "available", "required": false, "array": false}, {"key": "material", "type": "string", "status": "available", "required": false, "array": false, "size": 2000}, {"key": "storyText", "type": "string", "status": "available", "required": false, "array": false, "size": 80000}], "indexes": [{"key": "idx_poiId", "type": "key", "status": "available", "attributes": ["poiId"], "orders": ["ASC"]}]}, {"$id": "CoworkingSpaceDetails", "databaseId": "town_verse_atlas_db_dev", "name": "Coworking Space Details", "enabled": true, "documentSecurity": false, "$permissions": ["create(\"team:admins\")", "read(\"any\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "attributes": [{"key": "poiId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "dayPassPrice", "type": "string", "status": "available", "required": false, "array": false, "size": 500}, {"key": "membershipTypesJson", "type": "string", "status": "available", "required": false, "array": false, "size": 64000}, {"key": "desksCount", "type": "integer", "status": "available", "required": false, "array": false}, {"key": "meetingRooms", "type": "integer", "status": "available", "required": false, "array": false}], "indexes": [{"key": "idx_poiId", "type": "key", "status": "available", "attributes": ["poiId"], "orders": ["ASC"]}]}, {"$id": "TownConfigurations", "name": "Town Configurations", "databaseId": "town_verse_atlas_db_dev", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "documentSecurity": false, "attributes": [{"key": "townName", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "country", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "logoUrl", "type": "string", "status": "available", "required": true, "array": false, "size": 2048}, {"key": "primaryColorValue", "type": "integer", "status": "available", "required": true, "array": false}, {"key": "secondaryColorValue", "type": "integer", "status": "available", "required": true, "array": false}, {"key": "fontFamily", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "mapDefaultLatitude", "type": "double", "status": "available", "required": true, "array": false}, {"key": "mapDefaultLongitude", "type": "double", "status": "available", "required": true, "array": false}, {"key": "mapDefaultZoom", "type": "double", "status": "available", "required": true, "array": false}, {"key": "availableLanguages", "type": "string", "status": "available", "required": true, "array": true, "size": 255}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "currency", "type": "string", "status": "available", "required": true, "array": false, "size": 10}], "indexes": [{"key": "idx_townName", "type": "fulltext", "status": "available", "attributes": ["townName"], "orders": ["ASC"]}]}, {"$id": "Games", "name": "Games", "databaseId": "town_verse_atlas_db_dev", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "documentSecurity": false, "attributes": [{"key": "townId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "title", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "type", "type": "string", "status": "available", "required": true, "array": false, "size": 100}, {"key": "shortDescription", "type": "string", "status": "available", "required": true, "array": false, "size": 8000}, {"key": "coverImageUrl", "type": "string", "status": "available", "required": true, "array": false, "size": 2048}, {"key": "rewardPoints", "type": "integer", "status": "available", "required": true, "array": false}, {"key": "gameplayDetailsJson", "type": "string", "status": "available", "required": true, "array": false, "size": 500000}], "indexes": [{"key": "idx_townId", "type": "key", "status": "available", "attributes": ["townId"], "orders": ["ASC"]}, {"key": "idx_title", "type": "fulltext", "status": "available", "attributes": ["title"], "orders": ["ASC"]}, {"key": "idx_type", "type": "key", "status": "available", "attributes": ["type"], "orders": ["ASC"]}, {"key": "idx_townId_type", "type": "key", "status": "available", "attributes": ["townId", "type"], "orders": ["ASC", "ASC"]}]}, {"$id": "ARExperiences", "name": "AR Experiences", "databaseId": "town_verse_atlas_db_dev", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "documentSecurity": false, "attributes": [{"key": "townId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "experienceId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "name", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "description", "type": "string", "status": "available", "required": true, "array": false, "size": 80000}, {"key": "triggerType", "type": "string", "status": "available", "required": true, "array": false, "size": 100}, {"key": "triggerDetailsJson", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "assetBundleUrlIos", "type": "string", "status": "available", "required": false, "array": false, "size": 2048}, {"key": "assetBundleUrlAndroid", "type": "string", "status": "available", "required": false, "array": false, "size": 2048}], "indexes": [{"key": "idx_townId", "type": "key", "status": "available", "attributes": ["townId"], "orders": ["ASC"]}, {"key": "idx_experienceId", "type": "key", "status": "available", "attributes": ["experienceId"], "orders": ["ASC"]}, {"key": "idx_name", "type": "fulltext", "status": "available", "attributes": ["name"], "orders": ["ASC"]}, {"key": "idx_townId_expId", "type": "key", "status": "available", "attributes": ["townId", "experienceId"], "orders": ["ASC", "ASC"]}]}, {"$id": "Reviews", "name": "Reviews", "databaseId": "town_verse_atlas_db_dev", "$permissions": ["read(\"any\")", "create(\"users\")"], "documentSecurity": true, "attributes": [{"key": "townId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "entityId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "entityType", "type": "string", "status": "available", "required": true, "array": false, "size": 100}, {"key": "userId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "userName", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "rating", "type": "integer", "status": "available", "required": true, "array": false}, {"key": "comment", "type": "string", "status": "available", "required": false, "array": false, "size": 5000}, {"key": "reviewDate", "type": "datetime", "status": "available", "required": true, "array": false}], "indexes": [{"key": "idx_townId", "type": "key", "status": "available", "attributes": ["townId"], "orders": ["ASC"]}, {"key": "idx_entityId", "type": "key", "status": "available", "attributes": ["entityId"], "orders": ["ASC"]}, {"key": "idx_userId", "type": "key", "status": "available", "attributes": ["userId"], "orders": ["ASC"]}, {"key": "idx_entityType", "type": "key", "status": "available", "attributes": ["entityType"], "orders": ["ASC"]}, {"key": "idx_townId_entityId_type", "type": "key", "status": "available", "attributes": ["townId", "entityId", "entityType"], "orders": ["ASC", "ASC", "ASC"]}]}, {"$id": "Bookings", "name": "Bookings", "databaseId": "town_verse_atlas_db_dev", "$permissions": ["create(\"users\")"], "documentSecurity": true, "attributes": [{"key": "townId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "userId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "entityId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "entityType", "type": "string", "status": "available", "required": true, "array": false, "size": 100}, {"key": "bookingDetailsJson", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "status", "type": "string", "status": "available", "required": true, "array": false, "size": 1000}, {"key": "totalAmount", "type": "double", "status": "available", "required": false, "array": false}, {"key": "currency", "type": "string", "status": "available", "required": false, "array": false, "size": 10}, {"key": "bookingTimestamp", "type": "datetime", "status": "available", "required": true, "array": false}], "indexes": [{"key": "idx_townId", "type": "key", "status": "available", "attributes": ["townId"], "orders": ["ASC"]}, {"key": "idx_userId", "type": "key", "status": "available", "attributes": ["userId"], "orders": ["ASC"]}, {"key": "idx_entityId", "type": "key", "status": "available", "attributes": ["entityId"], "orders": ["ASC"]}, {"key": "idx_status", "type": "fulltext", "status": "available", "attributes": ["status"], "orders": ["ASC"]}, {"key": "idx_townId_userId", "type": "key", "status": "available", "attributes": ["townId", "userId"], "orders": ["ASC", "ASC"]}]}, {"$id": "UserGameProgress", "name": "User Game Progress", "databaseId": "town_verse_atlas_db_dev", "$permissions": ["create(\"users\")"], "documentSecurity": true, "attributes": [{"key": "townId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "userId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "gameId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "currentStepId", "type": "string", "status": "available", "required": false, "array": false, "size": 255}, {"key": "collectedItemsJson", "type": "string", "status": "available", "required": false, "array": false, "size": 4000}, {"key": "score", "type": "integer", "status": "available", "required": true, "array": false}, {"key": "isCompleted", "type": "boolean", "status": "available", "required": true, "array": false}], "indexes": [{"key": "idx_townId", "type": "key", "status": "available", "attributes": ["townId"], "orders": ["ASC"]}, {"key": "idx_userId", "type": "key", "status": "available", "attributes": ["userId"], "orders": ["ASC"]}, {"key": "idx_gameId", "type": "key", "status": "available", "attributes": ["gameId"], "orders": ["ASC"]}, {"key": "idx_townId_userId_gameId", "type": "key", "status": "available", "attributes": ["townId", "userId", "gameId"], "orders": ["ASC", "ASC", "ASC"]}]}, {"$id": "LoyaltyRewards", "name": "<PERSON><PERSON><PERSON>", "databaseId": "town_verse_atlas_db_dev", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "documentSecurity": false, "attributes": [{"key": "townId", "type": "string", "status": "available", "required": true, "array": false, "size": 255}, {"key": "title", "type": "string", "status": "available", "required": true, "array": false, "size": 4000}, {"key": "description", "type": "string", "status": "available", "required": true, "array": false, "size": 9000}, {"key": "pointsRequired", "type": "integer", "status": "available", "required": true, "array": false}, {"key": "iconUrl", "type": "string", "status": "available", "required": true, "array": false, "size": 2048}], "indexes": [{"key": "idx_townId", "type": "key", "status": "available", "attributes": ["townId"], "orders": ["ASC"]}, {"key": "idx_pointsRequired", "type": "key", "status": "available", "attributes": ["pointsRequired"], "orders": ["ASC"]}, {"key": "idx_townId_pointsReq", "type": "key", "status": "available", "attributes": ["townId", "pointsRequired"], "orders": ["ASC", "ASC"]}]}], "functions": [{"$id": "updateUserNames", "name": "updateUserNames", "runtime": "dart-3.0", "execute": [], "events": ["users.*.update.name"], "schedule": "", "timeout": 15, "enabled": true, "logging": true, "entrypoint": "lib/main.dart", "commands": "dart pub get && dart compile exe lib/main.dart -o /usr/local/src/lib/main", "ignore": [".packages", ".dart_tool"], "path": "appwrite_data_service/functions/updateUserNames"}], "buckets": [{"$id": "town-logos", "name": "Town Logos", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "fileSecurity": false, "enabled": true, "maximumFileSize": 2097152, "allowedFileExtensions": ["png", "jpg", "svg"], "compression": "gzip", "encryption": false, "antivirus": false}, {"$id": "poi-images", "name": "POI Images", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "create(\"team:business_owners\")", "update(\"team:admins\")", "update(\"team:business_owners\")", "delete(\"team:admins\")", "delete(\"team:business_owners\")"], "fileSecurity": false, "enabled": true, "maximumFileSize": 5242880, "allowedFileExtensions": ["jpg", "png", "webp"], "compression": "gzip", "encryption": false, "antivirus": false}, {"$id": "restaurant-images", "name": "Restaurant Images", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "create(\"team:business_owners\")", "update(\"team:admins\")", "update(\"team:business_owners\")", "delete(\"team:admins\")", "delete(\"team:business_owners\")"], "fileSecurity": false, "enabled": true, "maximumFileSize": 5242880, "allowedFileExtensions": ["jpg", "png", "webp"], "compression": "gzip", "encryption": false, "antivirus": false}, {"$id": "hotel-images", "name": "Hotel Images", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "create(\"team:business_owners\")", "update(\"team:admins\")", "update(\"team:business_owners\")", "delete(\"team:admins\")", "delete(\"team:business_owners\")"], "fileSecurity": false, "enabled": true, "maximumFileSize": 5242880, "allowedFileExtensions": ["jpg", "png", "webp"], "compression": "gzip", "encryption": false, "antivirus": false}, {"$id": "event-images", "name": "Event Images", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "create(\"team:event_organizers\")", "update(\"team:admins\")", "update(\"team:event_organizers\")", "delete(\"team:admins\")", "delete(\"team:event_organizers\")"], "fileSecurity": false, "enabled": true, "maximumFileSize": 5242880, "allowedFileExtensions": ["jpg", "png", "webp"], "compression": "gzip", "encryption": false, "antivirus": false}, {"$id": "game-assets", "name": "Game Assets", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "fileSecurity": false, "enabled": true, "maximumFileSize": 10485760, "allowedFileExtensions": ["jpg", "png", "lottie", "mp3"], "compression": "gzip", "encryption": false, "antivirus": false}, {"$id": "ar-experience-bundles", "name": "AR Experience Bundles", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "fileSecurity": false, "enabled": true, "maximumFileSize": 52428800, "allowedFileExtensions": ["assetbundle", "zip"], "compression": "gzip", "encryption": false, "antivirus": false}, {"$id": "loyalty-reward-icons", "name": "<PERSON><PERSON><PERSON>", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "fileSecurity": false, "enabled": true, "maximumFileSize": 1048576, "allowedFileExtensions": ["png", "svg"], "compression": "gzip", "encryption": false, "antivirus": false}, {"$id": "hiking-trail-thumbnails", "name": "Hiking Trail Thumbnails", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "fileSecurity": false, "enabled": true, "maximumFileSize": 2097152, "allowedFileExtensions": ["jpg", "png"], "compression": "gzip", "encryption": false, "antivirus": false}, {"$id": "user-profile-avatars", "name": "User Profile Avatars", "$permissions": ["read(\"any\")", "create(\"users\")", "update(\"users\")", "delete(\"users\")"], "fileSecurity": true, "enabled": true, "maximumFileSize": 2000000, "allowedFileExtensions": ["jpg", "jpeg", "png", "webp"], "compression": "zstd", "encryption": true, "antivirus": true}, {"$id": "poi_images", "name": "POI Images", "$permissions": ["read(\"any\")", "create(\"team:admins\")", "update(\"team:admins\")", "delete(\"team:admins\")"], "enabled": true, "fileSecurity": false, "maximumFileSize": 10000000, "allowedFileExtensions": ["jpg", "jpeg", "png", "webp"], "compression": "zstd", "encryption": true, "antivirus": true}]}