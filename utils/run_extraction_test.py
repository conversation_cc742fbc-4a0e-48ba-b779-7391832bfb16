import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from flows.extract import extract_pois_for_town

# Load environment variables
load_dotenv()

# Run extraction for Vianden with a small bounding box
# Coordinates for Vianden, Luxembourg
vianden_bbox = "6.19,49.93,6.22,49.95"  # left,bottom,right,top

print("Starting extraction for Vianden...")
try:
    extract_pois_for_town("vianden", vianden_bbox)
    print("Extraction completed successfully!")
except Exception as e:
    print(f"Error during extraction: {e}")
    import traceback
    traceback.print_exc()