import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

from atlas.connectors.foursquare import search_foursquare_places
from atlas.clients.mongo import get_mongo_db

def test_foursquare_and_storage():
    """Test Foursquare API call and data storage"""
    print("Testing Foursquare API call...")
    
    # Test with coordinates for "Fuerner Knupp" in Vianden
    query = "Fuerner Knupp, vianden"
    lat, lon = 49.9367452, 6.2046226
    
    try:
        # Call Foursquare API
        result = search_foursquare_places(query, lat, lon)
        print(f"Foursquare API result: {result}")
        
        if result:
            print("Foursquare API call successful!")
            print(f"Result keys: {result.keys()}")
            
            # Check for fsq_id or fsq_place_id
            fsq_id = result.get("fsq_id") or result.get("fsq_place_id")
            print(f"Foursquare ID found: {fsq_id}")
            
            # Try to store in MongoDB
            try:
                db = get_mongo_db()
                print("MongoDB connection successful!")
                
                # Try to upsert the data
                collection = db["source_foursquare_raw"]
                if fsq_id:
                    collection.update_one(
                        {"source_id": fsq_id},
                        {
                            "$set": {
                                "data": result,
                                "data_hash": "test_hash"
                            },
                            "$setOnInsert": {
                                "fetched_at": "test_timestamp"
                            }
                        },
                        upsert=True
                    )
                    print("Data stored in MongoDB successfully!")
                    
                    # Check if collection was created
                    collections = db.list_collection_names()
                    print(f"Available collections: {collections}")
                    
                    if "source_foursquare_raw" in collections:
                        count = collection.count_documents({})
                        print(f"source_foursquare_raw collection has {count} documents")
                else:
                    print("No Foursquare ID found, skipping storage")
            except Exception as e:
                print(f"Error storing data in MongoDB: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("No result from Foursquare API")
            
    except Exception as e:
        print(f"Error during Foursquare API call: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_foursquare_and_storage()