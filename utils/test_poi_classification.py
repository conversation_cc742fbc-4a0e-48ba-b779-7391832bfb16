#!/usr/bin/env python3
"""
Test script for the POI classification functionality.
"""

import os
import sys
import json
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from atlas.processing.poi_classifier import classify_poi_type, get_poi_group
from staging.poi_types import ALL_POI_TYPES, POI_GROUPS

def test_poi_classification():
    """Test the POI classification with sample data."""
    
    # Sample POI data from different sources
    test_pois = [
        {
            "name": "McDonald's",
            "tags": ["restaurant", "fast_food"],
            "categories": ["Food & Drink", "Fast Food"],
            "google_types": ["restaurant", "food", "point_of_interest"],
            "foursquare_categories": ["Food", "Burger Joint"]
        },
        {
            "name": "Hilton Hotel",
            "tags": ["hotel", "lodging"],
            "categories": ["Travel & Transport", "Hotel"],
            "google_types": ["lodging", "point_of_interest"],
            "foursquare_categories": ["Travel & Transport", "Hotel"]
        },
        {
            "name": "Central Park",
            "tags": ["park", "leisure"],
            "categories": ["Recreation", "Park"],
            "google_types": ["park", "tourist_attraction", "point_of_interest"],
            "foursquare_categories": ["Outdoors & Recreation", "Park"]
        },
        {
            "name": "Metropolitan Museum of Art",
            "tags": ["museum", "tourism"],
            "categories": ["Arts & Entertainment", "Museum"],
            "google_types": ["museum", "tourist_attraction", "point_of_interest"],
            "foursquare_categories": ["Arts & Entertainment", "Museum"]
        },
        {
            "name": "Generic Building",
            "tags": ["building"],
            "categories": ["Point of Interest"],
            "google_types": ["point_of_interest"],
            "foursquare_categories": ["Building"]
        }
    ]
    
    print("Testing POI Classification...")
    print("=" * 50)
    
    for i, poi_data in enumerate(test_pois, 1):
        print(f"\nTest POI {i}: {poi_data['name']}")
        print("-" * 30)
        
        # Show input data
        print("Input data:")
        for key, value in poi_data.items():
            if key != "name":
                print(f"  {key}: {value}")
        
        # Classify the POI
        try:
            classified_type, original_tags = classify_poi_type(poi_data)
            poi_group = get_poi_group(classified_type)
            
            print(f"\nResults:")
            print(f"  Classified Type: {classified_type}")
            print(f"  POI Group: {poi_group}")
            print(f"  Original Tags: {original_tags}")
            
            # Validate results
            if classified_type in ALL_POI_TYPES:
                print(f"  ✓ Valid POI type")
            else:
                print(f"  ✗ Invalid POI type: {classified_type}")
                
            # Check if group exists
            group_found = False
            for group, types in POI_GROUPS.items():
                if classified_type in types:
                    group_found = True
                    break
                    
            if group_found:
                print(f"  ✓ Valid POI group")
            else:
                print(f"  ✗ No group found for type: {classified_type}")
                
        except Exception as e:
            print(f"  ✗ Error during classification: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_poi_classification()