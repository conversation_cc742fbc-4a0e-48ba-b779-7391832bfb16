import os
import requests
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

FOURSQUARE_API_URL = "https://places-api.foursquare.com/places/search"

def search_foursquare_places_debug(query: str, lat: float, lon: float, limit: int = 1):
    """
    Searches for places on Foursquare near a given location.
    """
    api_key = os.getenv("FOURSQUARE_API_KEY")
    
    if not api_key:
        logger.warning("FOURSQUARE_API_KEY not found. Skipping Foursquare search.")
        return None

    headers = {
        "Authorization": "Bearer " + api_key,
        "accept": "application/json",
        "X-Places-Api-Version": "2025-06-17"
    }
    params = {
        "query": query,
        "ll": f"{lat},{lon}",
        "radius": 150,  # Search within 150 meters for precision
        "limit": limit,
    }

    try:
        response = requests.get(FOURSQUARE_API_URL, headers=headers, params=params, timeout=10)
        logger.info(f"Request URL: {response.url}")
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response headers: {response.headers}")
        
        response.raise_for_status()
        data = response.json()
        logger.info(f"Foursquare response structure: {data}")
        if data.get("results"):
            logger.info(f"Found {len(data['results'])} result(s) on Foursquare for '{query}'.")
            # Log the structure of the first result to understand the fields
            if data["results"]:
                logger.info(f"First result structure: {data['results'][0]}")
                logger.info(f"First result keys: {data['results'][0].keys()}")
                # Check if fsq_id exists
                if 'fsq_id' in data['results'][0]:
                    logger.info(f"fsq_id found: {data['results'][0]['fsq_id']}")
                else:
                    logger.info("fsq_id NOT found in the result")
            # Return the first result for simplicity, as it's the most likely match
            return data["results"][0] if limit == 1 else data["results"]
        else:
            logger.warning(f"No results found on Foursquare for '{query}'.")
            return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching data from Foursquare for '{query}': {e}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Response content: {e.response.text}")
        return None

# Test with coordinates that should match what was used in the logs
if __name__ == "__main__":
    # Test with coordinates that were used in the logs for "Fuerner Knupp"
    # From the logs: lat, lon = 49.9367452, 6.2046226
    result = search_foursquare_places_debug("Fuerner Knupp, vianden", 49.9367452, 6.2046226)
    print(f"Result: {result}")