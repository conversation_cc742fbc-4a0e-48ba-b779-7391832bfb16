import os
import sys

try:
    from dotenv import load_dotenv
    from appwrite.client import Client
    from appwrite.services.storage import Storage
    from appwrite.client import AppwriteException
    from appwrite.query import Query
except ImportError:
    print("Required packages are not installed. Please install them by running:")
    print("pip install -r requirements.txt")
    sys.exit(1)

# Add project root to python path to allow imports if needed, and for consistency
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# --- Configuration ---
load_dotenv()
POI_IMAGES_BUCKET_ID = "poi_images"

def get_appwrite_client_for_script():
    """Initializes and returns the Appwrite client and storage service for standalone scripts."""
    try:
        client = Client()
        client.set_endpoint(os.getenv("APPWRITE_ENDPOINT"))
        client.set_project(os.getenv("APPWRITE_PROJECT_ID"))
        client.set_key(os.getenv("APPWRITE_API_KEY"))
        
        storage = Storage(client)
        print("Appwrite client initialized successfully.")
        return storage
    except Exception as e:
        print(f"Failed to initialize Appwrite client: {e}")
        sys.exit(1)

def clear_bucket(storage: Storage, bucket_id: str):
    """Lists and deletes all files in a given storage bucket."""
    print(f"\nFetching files from bucket '{bucket_id}'...")
    
    total_deleted = 0
    while True:
        try:
            # Fetch files in batches of 100, which is the maximum limit
            file_list = storage.list_files(
                bucket_id=bucket_id,
                queries=[Query.limit(100)]
            )
            
            if not file_list['files']:
                print("No more files found in the bucket.")
                break
            
            num_files = len(file_list['files'])
            print(f"Found {num_files} files in this batch. Deleting...")

            for file in file_list['files']:
                try:
                    storage.delete_file(bucket_id, file['$id'])
                    print(f"  - Deleted file: {file['name']} (ID: {file['$id']})")
                    total_deleted += 1
                except AppwriteException as e:
                    print(f"  - FAILED to delete file {file['$id']}: {e.message}")
            
            # If we fetched fewer than the limit, we're on the last page
            if num_files < 100:
                break
                
        except AppwriteException as e:
            print(f"An error occurred while processing bucket '{bucket_id}': {e.message}")
            break
            
    print(f"\nFinished. Total files deleted: {total_deleted}")

if __name__ == "__main__":
    print("--- DANGER: This script will permanently delete ALL files from a bucket ---")
    print(f"Target Bucket ID: {POI_IMAGES_BUCKET_ID}")
    
    # User confirmation prompt to prevent accidental deletion
    confirm = input("Are you sure you want to proceed? Type 'yes' to confirm: ")
    if confirm.lower() != 'yes':
        print("Operation cancelled.")
        sys.exit(0)
    
    storage_service = get_appwrite_client_for_script()
    clear_bucket(storage_service, POI_IMAGES_BUCKET_ID)
