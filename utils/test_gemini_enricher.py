#!/usr/bin/env python3
"""
Test script for the Gemini enricher functionality.
This script tests both Gemini and OpenAI providers.
"""

import os
import sys
import json
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from atlas.processing.enricher import enrich_poi_with_llm

def test_enricher():
    """Test the enricher with a sample POI."""
    # Sample POI data
    poi_data = {
        'name': 'Central Park',
        'type': 'Park',
        'rating': 4.8
    }
    
    # Test with English language
    languages = ['en']
    
    print("Testing POI enrichment...")
    print(f"POI: {poi_data['name']}")
    print(f"Provider preference: {os.getenv('AI_PROVIDER', 'gemini')}")
    print("-" * 50)
    
    # Run the enrichment
    result = enrich_poi_with_llm(poi_data, languages)
    
    # Display results
    print("Enrichment completed!")
    if 'shortDescription' in result:
        short_desc = json.loads(result['shortDescription'])
        print(f"Short Description: {short_desc['en']}")
        
    if 'fullDescription' in result:
        full_desc = json.loads(result['fullDescription'])
        print(f"Full Description: {full_desc['en']}")
    
    return result

if __name__ == "__main__":
    test_enricher()