import os
import pymongo
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get MongoDB connection string and adjust for local execution
mongo_connection_string = os.getenv("MONGO_CONNECTION_STRING")
# Replace 'mongo' with 'localhost' for local execution
mongo_connection_string = mongo_connection_string.replace('mongo:', 'localhost:')
print(f"Mongo connection string: {mongo_connection_string}")

# Connect to MongoDB
try:
    client = pymongo.MongoClient(mongo_connection_string)
    db = client.townverse_atlas  # Assuming the database name is townverse_atlas
    
    # List collections
    collections = db.list_collection_names()
    print(f"Collections: {collections}")
    
    # Check if source_foursquare_raw collection exists
    if "source_foursquare_raw" in collections:
        # Count documents in the collection
        count = db.source_foursquare_raw.count_documents({})
        print(f"source_foursquare_raw collection has {count} documents")
        
        # Show first document if any exist
        if count > 0:
            first_doc = db.source_foursquare_raw.find_one()
            print(f"First document: {first_doc}")
    else:
        print("source_foursquare_raw collection does not exist")
        
    # Check poi_links collection
    if "poi_links" in collections:
        count = db.poi_links.count_documents({})
        print(f"poi_links collection has {count} documents")
        
        # Show documents with Foursquare links
        fsq_links = db.poi_links.find({"foursquareFsqId": {"$exists": True}})
        fsq_count = db.poi_links.count_documents({"foursquareFsqId": {"$exists": True}})
        print(f"Found {fsq_count} POI links with Foursquare IDs")
        
        # Show first few documents with Foursquare links
        for i, doc in enumerate(fsq_links):
            if i >= 3:  # Show only first 3
                break
            print(f"POI link with Foursquare: {doc}")
    else:
        print("poi_links collection does not exist")
        
except Exception as e:
    print(f"Error connecting to MongoDB: {e}")