# Use an official Python runtime as a parent image
FROM python:3.12-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file into the container at /app
COPY requirements.txt .

# Install system dependencies for pyvips
RUN apt-get update && apt-get install -y build-essential libvips-dev --no-install-recommends && rm -rf /var/lib/apt/lists/*

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application's code into the container at /app
COPY . .

# Keep the container running to allow for `docker-compose exec`
CMD ["tail", "-f", "/dev/null"]
