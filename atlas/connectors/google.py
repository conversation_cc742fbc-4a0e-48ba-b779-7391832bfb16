import os
import requests
from prefect import task, get_run_logger

GOOGLE_TEXT_SEARCH_URL = "https://places.googleapis.com/v1/places:searchText"
GOOGLE_PLACE_DETAILS_URL_BASE = "https://places.googleapis.com/v1/places"

@task
def find_google_place_id(query: str, lat: float, lon: float):
    """
    Finds a Google Place ID for a given query and location bias.
    """
    logger = get_run_logger()
    api_key = os.getenv("GOOGLE_PLACES_API_KEY")
    if not api_key:
        logger.warning("GOOGLE_PLACES_API_KEY not found. Skipping Google search.")
        return None

    headers = {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": api_key,
        "X-Goog-FieldMask": "places.id",
    }

    payload = {
        "textQuery": query,
        "locationBias": {
            "circle": {
                "center": {"latitude": lat, "longitude": lon},
                "radius": 5000.0,
            }
        },
    }

    try:
        response = requests.post(GOOGLE_TEXT_SEARCH_URL, json=payload, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()
        if data.get("places"):
            place_id = data["places"][0]["id"]
            logger.info(f"Found Google Place ID for '{query}': {place_id}")
            return place_id
        else:
            logger.warning(f"No Google Place ID found for query '{query}'.")
            if data.get('error'):
                logger.error(f"Google API error message: {data['error'].get('message')}")
            return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Error finding Google Place ID for '{query}': {e}")
        return None

@task
def get_google_place_details(place_id: str):
    """
    Fetches detailed information for a given Google Place ID.
    """
    logger = get_run_logger()
    api_key = os.getenv("GOOGLE_PLACES_API_KEY")
    if not api_key:
        logger.warning("GOOGLE_PLACES_API_KEY not found. Skipping Google details fetch.")
        return None
    
    fields = "id,displayName,location,viewport,rating,userRatingCount,types,regularOpeningHours,formattedAddress,websiteUri,internationalPhoneNumber,priceLevel,photos"
    headers = {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": api_key,
        "X-Goog-FieldMask": fields,
    }

    url = f"{GOOGLE_PLACE_DETAILS_URL_BASE}/{place_id}"

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()
        if not data.get("error"):
            logger.info(f"Successfully fetched details for Google Place ID: {place_id}")
            return data
        else:
            logger.error(f"Failed to get Google Place details for {place_id}. Error: {data.get('error')}")
            return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching Google Place details for {place_id}: {e}")
        return None
