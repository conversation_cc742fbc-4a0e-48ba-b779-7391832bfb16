import os
import requests
from prefect import task, get_run_logger

# It's recommended to use a more specific client like pexels-py in a real project,
# but requests is sufficient for this example and avoids adding a new dependency.

PEXELS_API_URL = "https://api.pexels.com/v1/search"

@task
def search_photo_for_poi(query: str):
    """
    Searches for a photo on Pexels that matches the given query.

    Args:
        query: The search term (e.g., POI name).

    Returns:
        The URL of the first photo found, or None if no photo is found or an error occurs.
    """
    logger = get_run_logger()
    api_key = os.getenv("PEXELS_API_KEY")

    if not api_key:
        logger.warning("PEXELS_API_KEY not found. Skipping image search.")
        return None

    headers = {
        "Authorization": api_key
    }
    params = {
        "query": query,
        "per_page": 1,
        "page": 1
    }

    try:
        response = requests.get(PEXELS_API_URL, headers=headers, params=params)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        data = response.json()

        if data["photos"]:
            photo_url = data["photos"][0]["src"]["original"]
            logger.info(f"Found photo for '{query}': {photo_url}")
            return photo_url
        else:
            logger.warning(f"No photo found on Pexels for query: '{query}'")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching photo from Pexels for query '{query}': {e}")
        return None
