import requests
from prefect import task, get_run_logger

OVERPASS_API_URL = "https://overpass-api.de/api/interpreter"

@task
def fetch_pois_from_osm(bbox: str):
    """
    Fetches POIs from OpenStreetMap's Overpass API within a given bounding box.

    Args:
        bbox: A string representing the bounding box, e.g., "49.92,6.19,49.94,6.22"
    """
    logger = get_run_logger()
    logger.info(f"Fetching POIs from OpenStreetMap for bbox: {bbox}")

    # Overpass QL query to find a comprehensive set of POIs.
    # This query looks for common tourist and local-relevant tags.
    query = f"""
        [out:json][timeout:25];
        (
          node[~"^(amenity|shop|tourism|historic|leisure|office|craft)$"~"."]({bbox});
          way[~"^(amenity|shop|tourism|historic|leisure|office|craft)$"~"."]({bbox});
          relation[~"^(amenity|shop|tourism|historic|leisure|office|craft)$"~"."]({bbox});
        );
        out center;
    """

    headers = {
        "User-Agent": "TownVerse Atlas Ingest Pipeline (github.com/your-repo)"
    }

    try:
        response = requests.post(OVERPASS_API_URL, data=query, headers=headers, timeout=30)
        response.raise_for_status()  # Raise an exception for bad status codes
        data = response.json()
        logger.info(f"Successfully fetched {len(data['elements'])} elements from OSM.")
        return data['elements']
    except requests.exceptions.RequestException as e:
        logger.error(f"An error occurred while fetching data from Overpass API: {e}")
        return []
