import os
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_gemini_client():
    """
    Initialize and return a Gemini client.
    """
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        raise ValueError("GEMINI_API_KEY not found in environment variables")
    
    genai.configure(api_key=api_key)
    return genai

def get_gemini_model():
    """
    Initialize and return a Gemini model instance.
    """
    client = get_gemini_client()
    # Using gemini-2.5-flash as requested
    model = client.GenerativeModel('gemini-2.5-flash')
    return model