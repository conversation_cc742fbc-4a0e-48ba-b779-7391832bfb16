import os
from appwrite.client import Client
from appwrite.services.databases import Databases
from appwrite.services.storage import Storage
from dotenv import load_dotenv
from prefect import get_run_logger

# Load environment variables from .env file at the module level
load_dotenv()

def get_appwrite_client():
    """
    Initializes and returns the Appwrite client, databases service, and storage service.
    """
    logger = get_run_logger()

    # Log environment variable status for debugging
    project_id = os.getenv("APPWRITE_PROJECT_ID")
    api_key_found = bool(os.getenv("APPWRITE_API_KEY"))
    logger.info(f"Read APPWRITE_PROJECT_ID: {'********' if project_id else 'NOT FOUND'}")
    # logger.info(f"Read APPWRITE_PROJECT_ID: {project_id}")
    logger.info(f"Read APPWRITE_API_KEY: {'Found' if api_key_found else 'NOT FOUND'}")
    # logger.info(f"Read APPWRITE_API_KEY: {os.getenv("APPWRITE_API_KEY")}")

    logger.info("Initializing Appwrite client...")
    client = Client()
    (client
        .set_endpoint(os.getenv("APPWRITE_ENDPOINT"))
        .set_project(project_id)
        .set_key(os.getenv("APPWRITE_API_KEY"))
    )
    
    databases = Databases(client)
    storage = Storage(client)
    logger.info("Appwrite client initialized.")
    return client, databases, storage

