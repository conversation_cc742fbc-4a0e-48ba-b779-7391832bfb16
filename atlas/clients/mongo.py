import os
from pymongo import MongoClient
from pymongo.database import Database
from prefect import task, get_run_logger
from dotenv import load_dotenv

load_dotenv()

# Global client instance to be reused
_mongo_client = None

def get_mongo_client():
    """Initializes and returns a MongoClient instance."""
    global _mongo_client
    if _mongo_client is None:
        connection_string = os.getenv("MONGO_CONNECTION_STRING")
        if not connection_string:
            raise ValueError("MONGO_CONNECTION_STRING environment variable not set.")
        _mongo_client = MongoClient(connection_string)
    return _mongo_client

@task
def get_mongo_db(db_name="townverse_atlas_pool"):
    """Returns a MongoDB database object."""
    logger = get_run_logger()
    try:
        client = get_mongo_client()
        # The ismaster command is cheap and does not require auth.
        client.admin.command('ismaster')
        logger.info("Successfully connected to MongoDB.")
        return client[db_name]
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise
