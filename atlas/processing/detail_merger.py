"""
Detail Merger Module for TownVerse Atlas
Handles merging of POI details based on POI groups
"""

import json
from prefect import task, get_run_logger
from staging.poi_types import POI_GROUPS, POI_TYPE_ATTRIBUTES

@task
def create_detail_document(merged_poi: dict, merged_details: dict = None) -> tuple:
    """
    Create a detail document based on the POI group and type.
    
    Args:
        merged_poi: The merged POI data
        merged_details: The merged details data from source APIs
        
    Returns:
        A tuple of (detail_collection_name, detail_document) or (None, None)
    """
    logger = get_run_logger()
    
    poi_type = merged_poi.get("type", "").lower()
    poi_group = merged_poi.get("group", "")
    
    if not poi_type or not poi_group:
        logger.warning(f"Cannot create detail document: missing type or group for POI {merged_poi.get('name')}")
        return None, None
    
    # Determine the detail collection name based on the group
    detail_collection_name = f"{poi_group}_details"
    
    # Create the base detail document
    detail_document = {
        "poiId": merged_poi.get("_id", merged_poi.get("osmId", "")),
        "townId": merged_poi.get("townId", "")
    }
    
    # Add common attributes for the group
    group_attributes = _get_group_attributes(poi_group)
    for attr in group_attributes:
        # Try to get the attribute from merged_details first, then from merged_poi
        value = None
        if merged_details and attr in merged_details:
            value = merged_details[attr]
        elif attr in merged_poi:
            value = merged_poi[attr]
            
        if value is not None:
            detail_document[attr] = value
    
    # Add type-specific attributes
    type_attributes = POI_TYPE_ATTRIBUTES.get(poi_type, [])
    for attr in type_attributes:
        # Try to get the attribute from merged_details first, then from merged_poi
        value = None
        if merged_details and attr in merged_details:
            value = merged_details[attr]
        elif attr in merged_poi:
            value = merged_poi[attr]
            
        if value is not None:
            detail_document[attr] = value
    
    logger.info(f"Created detail document for {merged_poi.get('name')} in collection {detail_collection_name}")
    
    return detail_collection_name, detail_document

def _get_group_attributes(group_name: str) -> list:
    """
    Get common attributes for a POI group.
    
    Args:
        group_name: The name of the POI group
        
    Returns:
        List of common attributes for the group
    """
    group_attributes = {
        "booking_venues": [
            "allowsBooking",
            "reservationsRequired",
            "seatingCapacity"
        ],
        "cultural_educational": [
            "educationalPrograms",
            "guidedTours",
            "exhibitions"
        ],
        "recreation_facilities": [
            "facilities",
            "activities",
            "equipmentRental"
        ],
        "commercial_establishments": [
            "services",
            "businessHours",
            "paymentMethods"
        ],
        "transportation_infrastructure": [
            "technicalSpecs",
            "accessibility",
            "operatingHours"
        ],
        "healthcare_facilities": [
            "servicesOffered",
            "emergencyServices",
            "insuranceAccepted"
        ],
        "outdoor_nature": [
            "difficulty",
            "accessibility",
            "facilities"
        ]
    }
    
    return group_attributes.get(group_name, [])