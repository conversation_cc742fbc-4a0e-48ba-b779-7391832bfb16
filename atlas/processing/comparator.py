import json
from typing import Dict, Any, Set
from prefect import task, get_run_logger


def normalize_value(value: Any) -> Any:
    """Normalize values for comparison, especially for JSON strings."""
    if isinstance(value, str):
        # Try to parse JSON strings for more accurate comparison
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    return value


def dicts_equal(dict1: Dict[str, Any], dict2: Dict[str, Any], ignore_keys: Set[str] = None) -> bool:
    """
    Compare two dictionaries for equality, ignoring specified keys.
    
    Args:
        dict1: First dictionary to compare
        dict2: Second dictionary to compare
        ignore_keys: Set of keys to ignore during comparison
        
    Returns:
        True if dictionaries are equal (ignoring specified keys), False otherwise
    """
    if ignore_keys is None:
        ignore_keys = set()
        
    # Get all keys from both dictionaries, excluding ignored keys
    all_keys = set(dict1.keys()) | set(dict2.keys())
    compare_keys = all_keys - ignore_keys
    
    # Compare each key
    for key in compare_keys:
        val1 = normalize_value(dict1.get(key))
        val2 = normalize_value(dict2.get(key))
        
        # Handle lists - sort them for comparison
        if isinstance(val1, list) and isinstance(val2, list):
            if sorted(val1) != sorted(val2):
                return False
        # Handle dictionaries
        elif isinstance(val1, dict) and isinstance(val2, dict):
            if val1 != val2:
                return False
        # Handle other types
        elif val1 != val2:
            return False
            
    return True


@task
def compare_poi_data(new_poi: Dict[str, Any], existing_poi: Dict[str, Any]) -> bool:
    """
    Compare new POI data with existing POI data to determine if they are the same.
    
    Args:
        new_poi: New POI data from the merge process
        existing_poi: Existing POI data from Appwrite
        
    Returns:
        True if the data is the same, False if there are differences
    """
    logger = get_run_logger()
    
    # Keys to ignore during comparison (these are metadata, not actual POI data)
    ignore_keys = {
        '$id', '$createdAt', '$updatedAt', '$permissions', 
        'last_merged_at', 'needs_appwrite_load', 'appwriteId'
    }
    
    # Compare the dictionaries
    are_equal = dicts_equal(new_poi, existing_poi, ignore_keys)
    
    if not are_equal:
        logger.info(f"POI '{new_poi.get('name')}' has changed and needs to be updated.")
    else:
        logger.info(f"POI '{new_poi.get('name')}' is unchanged.")
        
    return are_equal