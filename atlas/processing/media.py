import requests
import pyvips
from prefect import task, get_run_logger
from appwrite.client import Client
from appwrite.input_file import InputFile
from appwrite.services.storage import Storage
from appwrite.id import ID

from atlas.clients.appwrite import get_appwrite_client

# Configuration
POI_IMAGES_BUCKET_ID = "poi_images"
IMAGE_SIZES = {
    "thumbnail": 320,
    "medium": 1024,
    "hero": 1920,
}

@task
def process_image_from_bytes(image_bytes: bytes):
    """
    Takes raw image bytes, processes them into multiple WebP versions.
    Returns a dictionary mapping size name to processed image bytes.
    """
    logger = get_run_logger()
    processed_images = {}
    try:
        # Load image from buffer and enable fail-on-error for more robust handling
        image = pyvips.Image.new_from_buffer(image_bytes, "", access="sequential", fail=True)
        # Strip all metadata to reduce file size and remove potentially sensitive info
        image = image.copy(strip=True)

        for name, width in IMAGE_SIZES.items():
            # Resize while maintaining aspect ratio. Height is a loose constraint.
            resized_image = image.thumbnail_image(width, height=width * 2, size="down")
            # Convert to WebP format in memory. Q=75 is a good balance of quality and size.
            webp_buffer = resized_image.webpsave_buffer(Q=75, strip=True)
            processed_images[name] = webp_buffer
            logger.info(f"Created '{name}' version of image ({len(webp_buffer)} bytes).")
        
        return processed_images
    except pyvips.Error as e:
        logger.error(f"Failed to process image with pyvips: {e}", exc_info=True)
        return None

@task
def download_image(url: str):
    """
    Downloads an image from a URL and returns its content as bytes.

    Args:
        url: The URL of the image to download.

    Returns:
        The image content in bytes, or None if download fails.
    """
    logger = get_run_logger()
    if not url:
        return None
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        logger.info(f"Successfully downloaded image from {url}")
        return response.content
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to download image from {url}: {e}")
        return None

@task
def upload_image_to_appwrite(image_bytes: bytes, filename: str):
    """
    Uploads image bytes to the Appwrite storage bucket for POI images.

    Args:
        image_bytes: The image content as bytes.
        filename: The desired filename for the stored image.

    Returns:
        The Appwrite file ID if successful, otherwise None.
    """
    logger = get_run_logger()
    if not image_bytes:
        return None

    _, _, storage = get_appwrite_client()

    try:
        # Appwrite's InputFile can take bytes directly
        input_file = InputFile.from_bytes(image_bytes, filename=filename)
        
        # We use ID.unique() to avoid filename collisions
        file = storage.create_file(POI_IMAGES_BUCKET_ID, ID.unique(), input_file)
        file_id = file['$id']
        logger.info(f"Successfully uploaded '{filename}' to Appwrite Storage with ID: {file_id}")
        return file_id
    except Exception as e:
        logger.error(f"Failed to upload image to Appwrite: {e}")
        return None
