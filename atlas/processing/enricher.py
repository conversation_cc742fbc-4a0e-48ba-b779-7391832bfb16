import os
import json
from enum import Enum
from prefect import task, get_run_logger
from openai import OpenAI, OpenAIError
import google.generativeai as genai
from google.api_core import exceptions as google_exceptions

# A mapping from language codes to full language names for the prompt
LANGUAGE_MAP = {
    "en": "English",
    "fr": "French",
    "de": "German",
    "es": "Spanish",
    "it": "Italian",
    "nl": "Dutch",
    "pt": "Portuguese",
}

class AIProvider(Enum):
    GEMINI = "gemini"
    OPENAI = "openai"

def get_preferred_provider():
    """
    Get the preferred AI provider from environment variables.
    Defaults to Gemini if not specified.
    """
    provider_str = os.getenv("AI_PROVIDER", "gemini").lower()
    try:
        return AIProvider(provider_str)
    except ValueError:
        # Default to Gemini if invalid provider specified
        return AIProvider.GEMINI

def generate_with_gemini(prompt: str, poi_name: str, lang_name: str):
    """
    Generate content using Gemini 2.5 Flash.
    """
    logger = get_run_logger()
    api_key = os.getenv("GEMINI_API_KEY")
    
    if not api_key:
        raise ValueError("GEMINI_API_KEY not found in environment variables")
    
    genai.configure(api_key=api_key)
    model = genai.GenerativeModel('gemini-2.5-flash')
    
    # Configure safety settings to allow travel-related content
    safety_settings = [
        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_ONLY_HIGH"},
        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_ONLY_HIGH"},
        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_ONLY_HIGH"},
        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_ONLY_HIGH"},
    ]
    
    response = model.generate_content(
        prompt,
        generation_config=genai.types.GenerationConfig(
            temperature=0.7,
            response_mime_type="application/json"
        ),
        safety_settings=safety_settings
    )
    
    return response.text

def generate_with_openai(prompt: str, poi_name: str, lang_name: str):
    """
    Generate content using OpenAI GPT-4o.
    """
    logger = get_run_logger()
    api_key = os.getenv("OPENAI_API_KEY")
    
    if not api_key:
        raise ValueError("OPENAI_API_KEY not found in environment variables")
    
    client = OpenAI(api_key=api_key)
    
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You are a helpful travel copywriter assistant that returns only valid JSON."},
            {"role": "user", "content": prompt}
        ],
        response_format={"type": "json_object"},
        temperature=0.7,
    )
    
    return response.choices[0].message.content

@task
def enrich_poi_with_llm(poi_data: dict, languages: list):
    """
    Enriches a POI dictionary with short and full descriptions generated by an LLM
    in multiple languages. Uses Gemini as default with OpenAI as fallback.
    """
    logger = get_run_logger()
    
    if not languages:
        logger.warning("No languages provided for enrichment. Skipping.")
        return poi_data
    
    # Determine preferred provider
    preferred_provider = get_preferred_provider()
    logger.info(f"Using {preferred_provider.value} as preferred AI provider")
    
    short_descriptions = {}
    full_descriptions = {}
    
    # Extract some data for the prompt context
    poi_name = poi_data.get("name")
    poi_type = poi_data.get("type")
    rating = poi_data.get("rating", 0)
    # For restaurants, we might have cuisine info
    cuisine = ""
    if poi_type == "Restaurant" and poi_data.get("cuisineType"):
        try:
            # cuisineType is a JSON string of a list
            cuisine_list = json.loads(poi_data.get("cuisineType"))
            if cuisine_list:
                cuisine = f"- Cuisine: {', '.join(cuisine_list)}"
        except (json.JSONDecodeError, TypeError):
            pass # Ignore if it's not valid JSON
    
    for lang_code in languages:
        lang_name = LANGUAGE_MAP.get(lang_code, lang_code.capitalize())
        
        prompt = f"""
As a travel copywriter, write an engaging short (~60 words) and full (~200 words) description for the following point of interest in **{lang_name}**.

Data:
- Name: {poi_name}
- Type: {poi_type}
{cuisine}
- Rating: {rating}/5

Return the result ONLY as a valid JSON object with two keys: "shortDescription" and "fullDescription". Do not include any other text or markdown formatting.
"""
        
        # Try preferred provider first
        provider_to_try = preferred_provider
        descriptions = None
        
        while descriptions is None:
            try:
                logger.info(f"Generating descriptions for '{poi_name}' in {lang_name} using {provider_to_try.value}...")
                
                if provider_to_try == AIProvider.GEMINI:
                    content = generate_with_gemini(prompt, poi_name, lang_name)
                else:  # OPENAI
                    content = generate_with_openai(prompt, poi_name, lang_name)
                
                # Parse the JSON response
                descriptions = json.loads(content)
                
                if "shortDescription" in descriptions and "fullDescription" in descriptions:
                    short_descriptions[lang_code] = descriptions["shortDescription"]
                    full_descriptions[lang_code] = descriptions["fullDescription"]
                    logger.info(f"Successfully generated descriptions for '{poi_name}' in {lang_name} using {provider_to_try.value}.")
                else:
                    logger.warning(f"LLM response for {lang_name} was missing required keys. Response: {content}")
                    descriptions = None
                    
            except (ValueError, OpenAIError, google_exceptions.GoogleAPIError, json.JSONDecodeError) as e:
                logger.error(f"Error during LLM enrichment for '{poi_name}' in {lang_name} using {provider_to_try.value}: {e}")
                
                # If we tried the preferred provider and it failed, try the fallback
                if provider_to_try == preferred_provider:
                    if preferred_provider == AIProvider.GEMINI:
                        provider_to_try = AIProvider.OPENAI
                        logger.info(f"Falling back to OpenAI for '{poi_name}' in {lang_name}")
                    else:
                        provider_to_try = AIProvider.GEMINI
                        logger.info(f"Falling back to Gemini for '{poi_name}' in {lang_name}")
                else:
                    # Both providers failed
                    logger.error(f"Both AI providers failed for '{poi_name}' in {lang_name}. Skipping this language.")
                    break # Move to next language
    
    # Only overwrite if we got any successful results
    if short_descriptions:
        poi_data["shortDescription"] = json.dumps(short_descriptions)
    if full_descriptions:
        poi_data["fullDescription"] = json.dumps(full_descriptions)
    
    return poi_data