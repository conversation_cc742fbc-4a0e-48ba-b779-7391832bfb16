import json
from prefect import task, get_run_logger
from atlas.processing.poi_classifier import classify_poi_type, get_poi_group

# Define the order of priority. Lower index = higher priority.
SOURCE_PRIORITY = ["google", "foursquare", "osm"]

def is_value_empty(key: str, value):
    """
    Checks if a value for a given key is considered empty or default.
    This helps in deciding whether to overwrite a field with data from a lower-priority source.
    """
    if value is None:
        return True
    if isinstance(value, str) and not value.strip():
        return True
    if isinstance(value, (list, dict)) and not value:
        return True
    
    # Rating and reviews are default if they are 0
    if key in ["rating", "numberOfReviews"] and value == 0:
        return True
        
    # Check for default description strings
    if key in ["shortDescription", "fullDescription"] and isinstance(value, str):
        try:
            desc_json = json.loads(value)
            en_desc = desc_json.get("en", "")
            if not en_desc or "More details to come" in en_desc or "located in the area" in en_desc:
                return True
        except (json.JSONDecodeError, TypeError):
            # If it's not valid JSON or not a string, it's not a default we can check
            pass
            
    return False

@task
def merge_poi_data(poi_data_list: list) -> tuple:
    """
    Merges a list of POI data dictionaries from different sources into a single one,
    respecting the source priority.

    Args:
        poi_data_list: A list of tuples, where each tuple contains (canonical_poi, details_object).

    Returns:
        A tuple of (merged_poi, merged_details).
    """
    logger = get_run_logger()

    if not poi_data_list:
        return None, None

    # Filter out any None entries
    poi_data_list = [p for p in poi_data_list if p[0] is not None]
    if not poi_data_list:
        return None, None

    # Sort by source priority
    poi_data_list.sort(key=lambda p: SOURCE_PRIORITY.index(p[0]['source']) if p[0].get('source') in SOURCE_PRIORITY else 99)
    
    merged_poi = {}
    merged_details = {}

    # Get a set of all keys from all POI and details objects
    all_poi_keys = set()
    all_details_keys = set()
    for poi, details in poi_data_list:
        if poi:
            all_poi_keys.update(poi.keys())
        if details:
            all_details_keys.update(details.keys())

    # Merge POI fields: iterate through keys and find the best value from sorted sources
    for key in all_poi_keys:
        for poi, _ in poi_data_list:
            if poi and key in poi and not is_value_empty(key, poi.get(key)):
                merged_poi[key] = poi.get(key)
                break  # Move to next key once highest-priority value is found

    # Merge Details fields
    for key in all_details_keys:
        for _, details in poi_data_list:
            if details and key in details and not is_value_empty(key, details.get(key)):
                merged_details[key] = details.get(key)
                break
    
    # Combine `imagesIds` from all sources and deduplicate
    all_image_ids = []
    for poi, _ in poi_data_list:
        if poi and isinstance(poi.get("imagesIds"), list):
            all_image_ids.extend(poi.get("imagesIds"))
    
    if all_image_ids:
        merged_poi["imagesIds"] = sorted(list(set(all_image_ids)))
    
    # Collect all tags from different sources
    all_tags = []
    for poi, _ in poi_data_list:
        if poi and isinstance(poi.get("tags"), list):
            all_tags.extend(poi.get("tags"))
    
    # Deduplicate tags
    if all_tags:
        merged_poi["tags"] = sorted(list(set(all_tags)))
    
    # Use LLM to classify the POI type
    try:
        classified_type, original_tags = classify_poi_type(merged_poi)
        merged_poi["type"] = classified_type
        
        # Add original tags to our tags list
        if isinstance(merged_poi.get("tags"), list):
            merged_poi["tags"].extend(original_tags)
            merged_poi["tags"] = sorted(list(set(merged_poi["tags"])))
        else:
            merged_poi["tags"] = original_tags
            
        # Get the group for this POI type
        poi_group = get_poi_group(classified_type)
        merged_poi["group"] = poi_group
    except Exception as e:
        logger.error(f"Error classifying POI type: {e}")
        # Fallback to the original type if classification fails
        if "type" not in merged_poi and poi_data_list:
            merged_poi["type"] = poi_data_list[0][0].get("type", "unknown")
    
    # Ensure essential fields from the highest priority source are present if missed
    if poi_data_list:
        base_poi = poi_data_list[0][0]
        if base_poi:
            # Also copy over temporary processing fields like photo references
            for key in ["name", "latitude", "longitude", "townId", "osmId", "_photo_references"]:
                if key in base_poi and key not in merged_poi:
                     merged_poi[key] = base_poi[key]

    logger.info(f"Merged POI data for '{merged_poi.get('name')}'. Classified type: '{merged_poi.get('type')}'. Name source: '{merged_poi.get('source')}'.")
    
    return merged_poi, (merged_details if merged_details else None)