"""
POI Classifier Module for TownVerse Atlas
Uses Gemini Flash to classify POIs into standardized types
"""

import os
import json
import google.generativeai as genai
from typing import Dict, List, Tuple
from staging.poi_types import ALL_POI_TYPES, POI_CATEGORIES

def get_gemini_client():
    """
    Initialize and return a Gemini client.
    """
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        raise ValueError("GEMINI_API_KEY not found in environment variables")
    
    genai.configure(api_key=api_key)
    return genai

def classify_poi_type(poi_data: Dict) -> Tuple[str, List[str]]:
    """
    Classify a POI into our standardized types using Gemini Flash.
    
    Args:
        poi_data: Dictionary containing POI data from various APIs
        
    Returns:
        Tuple of (standardized_type, original_tags)
    """
    # Extract relevant information from POI data
    name = poi_data.get("name", "")
    categories = []
    tags = []
    
    # Collect category/type information from different sources
    if "tags" in poi_data:
        tags.extend(poi_data["tags"])
    if "categories" in poi_data:
        categories.extend(poi_data["categories"])
    if "types" in poi_data:
        categories.extend(poi_data["types"])
    if "category" in poi_data:
        categories.append(poi_data["category"])
        
    # Get type information from different API sources
    if "osm_type" in poi_data:
        tags.append(poi_data["osm_type"])
    if "google_types" in poi_data:
        tags.extend(poi_data["google_types"])
    if "foursquare_categories" in poi_data:
        tags.extend(poi_data["foursquare_categories"])
        
    # Create prompt for Gemini
    prompt = f"""
    You are a Point of Interest (POI) classifier for a travel and tourism application.
    Classify the following POI into one of our standardized types.
    IMPORTANT: You MUST choose a specific type from our list. DO NOT use "other" or any generic type.
    
    POI Information:
    Name: {name}
    Categories/Types from APIs: {', '.join(categories + tags)}
    
    Our standardized POI types are:
    {', '.join(ALL_POI_TYPES)}
    
    Please respond ONLY with the single most appropriate standardized type from our list.
    Do not include any other text, explanation, or formatting.
    """
    
    try:
        # Initialize Gemini client
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Configure safety settings
        safety_settings = [
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_ONLY_HIGH"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_ONLY_HIGH"},
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_ONLY_HIGH"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_ONLY_HIGH"},
        ]
        
        # Generate response
        response = model.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=0.2,
                max_output_tokens=50
            ),
            safety_settings=safety_settings
        )
        
        # Extract the classified type
        classified_type = response.text.strip().lower().replace(" ", "_").replace("-", "_")
        
        # Validate that the classified type is in our list and is not "other"
        if classified_type in ALL_POI_TYPES and classified_type != "other":
            return classified_type, list(set(categories + tags))
        else:
            # If classification failed or returned "other", use a fallback approach
            return _fallback_classification(poi_data), list(set(categories + tags))
            
    except Exception as e:
        # Fallback classification if Gemini fails
        return _fallback_classification(poi_data), list(set(categories + tags))

def _fallback_classification(poi_data: Dict) -> str:
    """
    Fallback classification method when Gemini is unavailable or fails.
    Ensures every POI gets a specific type, never "other".
    """
    name = poi_data.get("name", "").lower()
    categories = []
    tags = []
    
    # Collect all category/type information
    if "tags" in poi_data:
        tags.extend([t.lower() for t in poi_data["tags"]])
    if "categories" in poi_data:
        categories.extend([c.lower() for c in poi_data["categories"]])
    if "types" in poi_data:
        categories.extend([t.lower() for t in poi_data["types"]])
        
    all_terms = [name] + categories + tags
    
    # Simple keyword-based classification with more specific types
    if any(term in all_terms for term in ["restaurant", "cafe", "coffee", "diner", "bistro", "eat", "food"]):
        return "restaurant"
    elif any(term in all_terms for term in ["hotel", "motel", "inn", "lodging", "hostel", "resort"]):
        return "hotel"
    elif any(term in all_terms for term in ["museum", "gallery", "exhibition", "art"]):
        return "museum"
    elif any(term in all_terms for term in ["park", "garden", "trail", "nature", "forest", "beach"]):
        return "park"
    elif any(term in all_terms for term in ["hospital", "clinic", "medical", "doctor", "health"]):
        return "hospital"
    elif any(term in all_terms for term in ["school", "university", "college", "education"]):
        return "school"
    elif any(term in all_terms for term in ["church", "mosque", "temple", "synagogue", "religious"]):
        return "church"
    elif any(term in all_terms for term in ["shop", "store", "market", "mall", "boutique", "retail"]):
        return "shopping_center"
    elif any(term in all_terms for term in ["airport", "station", "bus", "transport", "train", "flight"]):
        return "airport"
    elif any(term in all_terms for term in ["bank", "atm", "financial", "money"]):
        return "bank"
    elif any(term in all_terms for term in ["post", "mail", "postal"]):
        return "post_office"
    elif any(term in all_terms for term in ["bar", "pub", "nightclub", "club"]):
        return "bar_pub"
    elif any(term in all_terms for term in ["library", "book"]):
        return "library"
    elif any(term in all_terms for term in ["pharmacy", "drug", "medicine"]):
        return "pharmacy"
    elif any(term in all_terms for term in ["cinema", "theater", "movie"]):
        return "theater_cinema"
    elif any(term in all_terms for term in ["gym", "fitness", "sport"]):
        return "gym_fitness_center"
    elif any(term in all_terms for term in ["attraction", "tourist", "landmark", "monument"]):
        return "historical_site"
    elif any(term in all_terms for term in ["market", "farmers", "flea"]):
        return "market"
    else:
        # Default to attraction for truly generic points of interest
        return "historical_site"

def get_poi_group(poi_type: str) -> str:
    """
    Get the group/category for a given POI type.
    
    Args:
        poi_type: Standardized POI type
        
    Returns:
        Group name for the POI type
    """
    from staging.poi_types import POI_GROUPS
    
    for group, types in POI_GROUPS.items():
        if poi_type in types:
            return group
    
    # Default group if not found
    return "other"