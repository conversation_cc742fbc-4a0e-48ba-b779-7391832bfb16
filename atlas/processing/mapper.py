import json
from prefect import task, get_run_logger


@task
def map_osm_to_canonical(osm_element: dict, town_id: str) -> tuple:
    """
    Maps a raw OSM data element to the canonical TownVerse POI model.
    It also creates a related details object if applicable (e.g., for restaurants).

    Args:
        osm_element: A dictionary representing a single element from the Overpass API.
        town_id: The ID of the town this POI belongs to.

    Returns:
        A tuple containing the standardized POI dictionary and a details dictionary (or None).
    """
    logger = get_run_logger()
    tags = osm_element.get("tags", {})
    
    # Determine lat/lon, using center for ways/relations
    if osm_element["type"] == "node":
        lat = osm_element.get("lat")
        lon = osm_element.get("lon")
    else:
        center = osm_element.get("center", {})
        lat = center.get("lat")
        lon = center.get("lon")

    if not lat or not lon or not tags.get("name"):
        logger.warning(f"Skipping OSM element {osm_element.get('id')} due to missing essential data (name, lat, or lon).")
        return None, None

    logger.info(f"OSM Id: {osm_element.get('id')} Name: {tags.get('name')} Lat: {lat} Lon: {lon}")

    # Map OSM amenity to our internal 'type' classification.
    # This can be expanded into a more sophisticated mapping logic later.
    poi_type = tags.get("amenity", "unknown").replace("_", " ").title()
    
    # Collect tags for the new classification system
    osm_tags = []
    for key, value in tags.items():
        if key.startswith("amenity") or key.startswith("tourism") or key.startswith("shop") or key.startswith("leisure"):
            osm_tags.append(f"{key}:{value}")

    restaurant_details = None
    if poi_type == "Restaurant":
        # Process cuisine tag into a JSON array string
        cuisine_str = tags.get("cuisine", "")
        # Split by ';', trim whitespace, capitalize
        cuisine_list = [c.strip().replace('_', ' ').title() for c in cuisine_str.split(';') if c.strip()]

        restaurant_details = {
            "cuisineType": json.dumps(cuisine_list if cuisine_list else ["Not Specified"]),
            "allowsBooking": False,  # Default value
            "hasMenu": False,  # Default value
        }

    # Create the canonical model with defaults for fields not in OSM
    canonical_poi = {
        "townId": town_id,
        "name": tags.get("name"),
        "type": poi_type,
        "latitude": lat,
        "longitude": lon,
        "shortDescription": json.dumps({"en": f"A {poi_type} located in the area."}),
        "fullDescription": json.dumps({"en": f"{tags.get('name')} is a {poi_type} that offers a unique experience. More details to come."}),
        "imagesIds": [],
        "rating": 0.0,
        "numberOfReviews": 0,
        "hasARExperience": False,
        # Add other fields from your POIs collection with default values
        "openingHours": tags.get("opening_hours", None),
        "entryFee": None, 
        "arExperienceId": None,
        # Internal fields for processing
        "osmId": str(osm_element.get("id")),
        "source": "osm",
        # Tags for new classification system
        "tags": osm_tags
    }

    return canonical_poi, restaurant_details


@task
def map_google_to_canonical(google_place: dict, town_id: str, osm_id: str = None) -> tuple:
    """
    Maps a raw Google Place details object to the canonical TownVerse POI model.
    """
    logger = get_run_logger()
    if not google_place:
        return None, None
    
    tags = google_place
    lat = tags.get("geometry", {}).get("location", {}).get("lat")
    lon = tags.get("geometry", {}).get("location", {}).get("lng")
    name = tags.get("name")

    if not all([lat, lon, name]):
        logger.warning(f"Skipping Google Place {tags.get('place_id')} due to missing essential data.")
        return None, None
        
    types = tags.get("types", [])
    poi_type = "Attraction"  # Default type
    if "restaurant" in types:
        poi_type = "Restaurant"
    elif "lodging" in types:
        poi_type = "Hotel"
    elif "cafe" in types:
        poi_type = "Cafe"
    elif "bar" in types:
        poi_type = "Bar"
    elif "museum" in types:
        poi_type = "Museum"
    elif "park" in types:
        poi_type = "Park"

    photo_references = [photo['photo_reference'] for photo in tags.get('photos', [])]

    canonical_poi = {
        "townId": town_id,
        "name": name,
        "type": poi_type,
        "latitude": lat,
        "longitude": lon,
        "shortDescription": json.dumps({"en": ""}),
        "fullDescription": json.dumps({"en": ""}),
        "imagesIds": [],
        "rating": tags.get("rating", 0.0),
        "numberOfReviews": tags.get("user_ratings_total", 0),
        "hasARExperience": False,
        "openingHours": json.dumps(tags.get("opening_hours", {}).get("weekday_text", [])),
        "entryFee": None,
        "arExperienceId": None,
        "osmId": osm_id,
        "googlePlaceId": tags.get("place_id"),
        "source": "google",
        "_photo_references": photo_references,  # Temporary field for processing
        # Tags for new classification system
        "tags": types
    }

    restaurant_details = None
    if poi_type == "Restaurant":
        restaurant_details = {
            "priceRange": str(tags.get("price_level", "")),  # Google uses integer price levels
            "allowsBooking": False, # Not directly available
            "hasMenu": bool(tags.get("website")),  # Use website as a proxy for having a menu
        }

    return canonical_poi, restaurant_details


@task
def map_foursquare_to_canonical(foursquare_place: dict, town_id: str, osm_id: str = None) -> tuple:
    """
    Maps a raw Foursquare Place object to the canonical TownVerse POI model.
    """
    logger = get_run_logger()
    if not foursquare_place:
        return None, None

    tags = foursquare_place
    geocodes = tags.get("geocodes", {}).get("main", {})
    # The place search endpoint returns lat/lon at the top level, while other endpoints might use the geocodes structure.
    # This logic handles both cases for robustness.
    lat = geocodes.get("latitude") or tags.get("latitude")
    lon = geocodes.get("longitude") or tags.get("longitude")
    name = tags.get("name")

    if not all([lat, lon, name]):
        logger.warning(f"Skipping Foursquare Place {tags.get('fsq_place_id')} due to missing essential data. Values found: name='{name}', lat='{lat}', lon='{lon}'")
        return None, None

    categories = tags.get("categories", [])
    poi_type = categories[0]['name'] if categories else "Attraction"
    
    # Collect Foursquare categories for tags
    fs_categories = [cat['name'] for cat in categories]

    canonical_poi = {
        "townId": town_id,
        "name": name,
        "type": poi_type,
        "latitude": lat,
        "longitude": lon,
        "shortDescription": json.dumps({"en": ""}),
        "fullDescription": json.dumps({"en": ""}),
        "imagesIds": [],
        "rating": tags.get("rating", 0.0) / 2,  # Foursquare is out of 10, Appwrite is out of 5
        "numberOfReviews": tags.get("stats", {}).get("total_ratings", 0),
        "hasARExperience": False,
        "openingHours": None, # Not in standard search result
        "entryFee": None,
        "arExperienceId": None,
        "osmId": osm_id,
        "foursquareFsqId": tags.get("fsq_place_id"),
        "source": "foursquare",
        # Tags for new classification system
        "tags": fs_categories
    }

    restaurant_details = None
    if poi_type == "Restaurant":
        restaurant_details = {
            "priceRange": str(tags.get("price", "")),  # Foursquare uses 1-4 scale as string
        }

    return canonical_poi, restaurant_details