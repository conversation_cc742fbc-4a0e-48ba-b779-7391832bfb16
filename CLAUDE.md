# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TownVerse Atlas is a data ingestion and enrichment pipeline for the TownVerse platform. It automatically populates TownVerse with comprehensive, accurate, and engaging data for Points of Interest (POIs) like restaurants, hotels, museums, and landmarks.

The pipeline operates in a series of orchestrated stages:
1. Fetch raw POI data from multiple public APIs and open data sources
2. Normalize and deduplicate data from all sources to a canonical schema
3. Enrich data with AI-generated descriptions using Gemini 2.5 Flash (with GPT-4o as fallback)
4. Process and optimize images for web and mobile
5. Upsert final data into Appwrite database collections

## Tech Stack

- **Core Language**: Python 3.12+
- **Orchestration**: Prefect 2
- **Containerization**: Docker
- **Backend**: Appwrite (Database & Storage)
- **Data Processing**: Pandas
- **AI/ML**: Google Gemini API (Gemini 2.5 Flash with OpenAI GPT-4o as fallback)
- **Image Processing**: pyvips
- **APIs**: requests, appwrite-sdk

## Project Structure

```
townverse-atlas/
├── atlas/                  # Core Python source package
│   ├── clients/            # Appwrite and MongoDB clients
│   ├── connectors/         # Modules for fetching data from sources
│   └── processing/         # Modules for data processing
├── flows/                  # Prefect flows that define the pipeline
├── staging/                # Data models for staging
└── utils/                  # Utility scripts
```

## Common Development Commands

### Running the Pipeline

The project uses Docker Compose to simplify the management of the application and its MongoDB dependency:

```bash
# Start services
docker-compose up -d --build

# Execute ingestion flows (example for Vianden, Luxembourg)
# 1. Extract raw data from sources into MongoDB
docker-compose exec atlas python -c 'from flows.extract import extract_pois_for_town; extract_pois_for_town("vianden", "49.92,6.19,49.94,6.22")'

# 2. Transform raw data into merged POIs
docker-compose exec atlas python -c 'from flows.transform import transform_and_merge_pois; transform_and_merge_pois("vianden")'

# 3. Load merged POIs into Appwrite
docker-compose exec atlas python -c 'from flows.load import load_pois_to_appwrite; load_pois_to_appwrite("vianden")'

# View logs
docker-compose logs -f atlas

# Stop services
docker-compose down -v
```

### Development Environment

1. Configure environment variables by copying `.env.example` to `.env` and filling in your API keys
2. Ensure Docker Desktop is running
3. Run `docker-compose up -d --build` to start services

## Core Architecture Components

### Flows (in `flows/`)
- `extract.py`: Fetches raw data from OSM, Google Places, and Foursquare APIs
- `transform.py`: Normalizes, merges, and enriches data using LLMs
- `load.py`: Upserts final data into Appwrite with soft delete handling

### Connectors (in `atlas/connectors/`)
- `osm.py`: OpenStreetMap connector using Overpass API
- `google.py`: Google Places API connector
- `foursquare.py`: Foursquare Places API connector

### Processing Modules (in `atlas/processing/`)
- `mapper.py`: Maps data from various sources to canonical model
- `merger.py`: Merges data from multiple sources with priority logic
- `enricher.py`: Generates descriptions using Google Gemini 2.5 Flash (with OpenAI GPT-4o as fallback)
- `media.py`: Processes and uploads images to Appwrite storage

### Data Flow
1. Raw data is stored in MongoDB staging collections
2. Data is mapped to canonical schema
3. Multiple source data is merged with priority logic
4. Data is enriched with LLM-generated descriptions
5. Images are processed and uploaded to Appwrite storage
6. Final data is upserted to Appwrite database collections